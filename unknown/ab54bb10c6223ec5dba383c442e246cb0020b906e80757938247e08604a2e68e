import React, { useState, useEffect, useMemo } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { Sticky, StickyContainer } from 'react-sticky';
import { useSelector } from "react-redux";
import CardOverlay from '../pages/CardOverlay';
import FilterLocation from '../pages/FilterLocation';
import ObservationModal from '../pages/ObservationModal';
import IncidentReport from '../pages/IncidentReport';
import IncidentCardReport from '../pages/IncidentCardReport';
import PermitModal from '../pages/PermitModal';
import API from '../services/API';
import {
    OBSERVATION_REPORT_URL,
    OBSERVATION_REPORT_WITH_ID,
    STATIC_URL,
    ACTION_URL,
    PERMIT_REPORT_WITH_ID,
    REPORT_INCIDENT_LEAD_INVESTIGATOR_URL,
    REPORT_INCIDENT_REVIEW_URL,
    GET_REVIEWER_INCIDENT,
    GET_REPORTER_INCIDENT,
    GET_INCIDENT_OWNER_INCIDENT
} from '../constants';
import ActionGrid from './ActionGrid';
import ActionLog from './ActionLog';
import IncidentInvestigationCard from './IncidentInvestigationCard';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { Link, withRouter } from 'react-router-dom';

const ActionList = () => {
    const [obsCount, setObsCount] = useState(0)
    const [eptwCount, setEptwCount] = useState(0)
    const [incidentCount, setIncidentCount] = useState(0)
    const [inspectionCount, setInspectionCount] = useState(0)
    const [auditCount, setAuditCount] = useState(0)
    const [auditFindingCount, setAuditFindingCount] = useState(0)
    const [plantCount, setPlantCount] = useState(0)

    const [action, setAction] = useState([])
    const [date, setDate] = useState([])
    const [total, setTotal] = useState(0)

    const me = useSelector((state) => state.login.user)
    const isIncidentReview = useMemo(() => {
        return me?.validationRoles?.some(item => item.name === 'Incident Reviewer') || false;
    }, [me]);

    const isIncidentTrigger = useMemo(() => {
        return me?.validationRoles?.some(item => item.name?.includes('Incident Owner') || item.name === 'Group EHS Team' || item.name === 'Country EHS Director') || false;
    }, [me]);

    const validationRoles = me?.validationRoles || [];
    const isReporter = validationRoles.some(role => role.name === 'Statistics Reporter');
    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {

    };

    const currentDate = moment();
    const [obsData, setObsData] = useState([]);
    const [actionData, setActionData] = useState([]);
    const [rendered, setRendered] = useState(0)
    const [combinedData, setCombinedData] = useState([])
    useEffect(() => {
        getObservationData();
        getActionData();
    }, [])

    const getObservationData = async () => {
        const response = await API.get(OBSERVATION_REPORT_URL);
        if (response.status === 200) {
            setObsData(response.data)

        }
    }

    const getActionData = async () => {



        const response = await API.get(ACTION_URL);
        if (response.status === 200) {
            setActionData(response.data)

        }
    }

    useEffect(() => {

        getActionData()
        fetchDataCountForIncident();
    }, [
        rendered
    ])


    const [value, setValue] = useState(0);


    const handleChange = (event, newValue) => {
        setValue(newValue);
    };

    const getFilteredActions = (applicationType, statusList) => {
        return actionData.filter(action =>
            action.application === applicationType && statusList.includes(action.status)
        );
    }
    const formatDateTime = (dateTimeString) => {

        const dateTimeParts = dateTimeString.split(' ');
        let dateParts = ''
        const regex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;

        if (regex.test(dateTimeString)) {
            dateParts = dateTimeParts[0].split('/');
        } else {
            dateParts = dateTimeParts[0].split('-');
        }

        const day = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10);
        const year = parseInt(dateParts[2], 10);

        // Format day with leading zero if needed
        const formattedDay = (day < 10 ? '0' : '') + day;
        // Get month abbreviation
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const formattedMonth = monthNames[month - 1];
        // Format year
        const formattedYear = year;

        return `${formattedDay} ${formattedMonth}, ${formattedYear}`;
    }
    const getActions = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {


            const obs = response.data
                .filter(i => i.application === 'Observation' && i.status !== 'completed')
                .reverse()
            setObsCount(obs.length);
            const incident = response.data
                .filter(i => i.application === 'INCIDENT' && i.status !== 'completed')
                .reverse()
            setIncidentCount(incident.length);
            const eptw = response.data
                .filter(i => i.application === 'PermitToWork' && i.status !== 'completed')
                .reverse()
            setEptwCount(eptw.length);
            const inspection = response.data
                .filter(i => i.application === 'Inspection' && i.status !== 'completed')
                .reverse()
            setInspectionCount(inspection.length);
            const audit = response.data
                .filter(i => i.application === 'Audit' && i.status !== 'completed')
                .reverse()
            setAuditCount(audit.length);
            const auditFinding = response.data
                .filter(i => i.application === 'AuditFinding' && i.status !== 'completed')
                .reverse()
            setAuditFindingCount(auditFinding.length);
            const plant = response.data
                .filter(i => i.application === 'Plant' && i.status !== 'completed')
                .reverse()
            setPlantCount(plant.length);


        }

    }



    useEffect(() => {
        myAction();

    }, [])
    const myAction = async () => {
        const response = await API.get(ACTION_URL);
        if (response.status === 200) {
            setTotal(response.data.length)

            const actions = response.data.sort((a, b) => new Date(b.createdDate) - new Date(a.createdDate));

            const groupedActions = actions.reduce((acc, action) => {
                const [date, time] = action.createdDate.split(' '); // Extracting date and time parts
                const formattedDate = formatDateTime(date);
                if (!acc[formattedDate]) {
                    acc[formattedDate] = [];
                }
                acc[formattedDate].push({ ...action, time });
                return acc;
            }, {});


            // setDate(Object.keys(groupedActions).reverse())

            const sortedDates = Object.keys(groupedActions).reverse().sort((a, b) => {
                const dateA = new Date(a);
                const dateB = new Date(b);
                return dateB - dateA; // Sorting in descending order
            });

            setDate(sortedDates)

            setAction(groupedActions)

        }
    }

    const fetchDataCountForIncident = async () => {
        try {
            const [actionDataResponse, incidentReviewResponse, investigationResponse, incidentReporter, triggerIncident] = await Promise.all([
                getFilteredActions('INCIDENT', ['open', 'returned']),
                fetchIncidentReviewData(),
                fetchInvestigationData(),
                fetchIncidentReporterData(),
                fetchTriggerIncidentData()
            ]);

            // Assuming all responses are in the correct format and do not require transformation
            setCombinedData([...actionDataResponse, ...incidentReviewResponse, ...investigationResponse, ...incidentReporter, ...triggerIncident]);

        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };



    const fetchTriggerIncidentData = async () => {
        const response = await API.get(GET_INCIDENT_OWNER_INCIDENT);

        return response.status === 200 && isIncidentTrigger ? response.data.filter(i => i.status === 'Reviewed').map(i => {
            return {
                actionType: 'trigger_incident',
                application: 'INCIDENT',
                applicationDetails: i,
                createdDate: i.created,
                description: '',
                objectId: i.id
            }
        }) : [];
    };

    const fetchIncidentReviewData = async () => {
        const response = await API.get(GET_REVIEWER_INCIDENT);

        return response.status === 200 && isIncidentReview ? response.data.map(i => {
            return {
                actionType: 'review_incident',
                application: 'INCIDENT',
                applicationDetails: i,
                createdDate: i.created,
                description: '',
                objectId: i.id
            }
        }) : [];
    };

    const fetchIncidentReporterData = async () => {
        const response = await API.get(GET_REPORTER_INCIDENT);

        return response.status === 200 && response.data.map(i => {
            return {
                actionType: 'modify_incident',
                application: 'INCIDENT',
                applicationDetails: i,
                createdDate: i.created,
                description: '',
                objectId: i.id
            }
        });
    };
    const fetchInvestigationData = async () => {
        const params = { "include": [{ "relation": "user" }] };
        const response = await API.get(`${REPORT_INCIDENT_LEAD_INVESTIGATOR_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);



        return response.status === 200 ? response.data.filter(i => i.status !== 'Investigation Completed').map(i => {
            return {
                actionType: 'conduct_investigation',
                application: 'INCIDENT',
                applicationDetails: i,
                createdDate: i.created,
                description: i.investigationRemarks,
                objectId: i.id
            }
        }) : [];
    };
    return (
        <>
            <h5 className='mt-4 fw-bold actionTitle'>My Actions</h5>
            <p className='mb-5 actionDesc'>Click on the specific application to see a listing of actions that you need to take.</p>

            <div className='row'>

                <ActionGrid count={getFilteredActions('Observation', ['open', 'returned']).length} title={'Observation'} url={'/apps/ehs'} desc={'Record and track observations seamlessly for improved workplace safety and efficiency.'} color={'#EF4444'} />
                <ActionGrid count={combinedData.length} title={'Incident'} url={'/apps/incident'} desc={'Report and manage incidents effectively to mitigate risks and enhance safety measures.'} color={'#60A5FA'} />
                <ActionGrid count={getFilteredActions('PermitToWork', ['open', 'returned']).length} title={'ePermit to Work'} url={'/apps/eptw'} desc={'Streamline permit management processes to ensure safety and compliance.'} color={'#FBBF24'} />
                <ActionGrid count={getFilteredActions('Inspection', ['open', 'returned']).length} title={'Inspection'} url={'/apps/inspection'} desc={'Streamline inspections for effective compliance and operational efficiency.'} color={'#A78BFA'} />
                <ActionGrid count={getFilteredActions('Audit', ['open', 'returned']).length + getFilteredActions('AuditFinding', ['open', 'returned']).length} title={'Audit'} url={'/apps/audit'} desc={'Enhance risk management and resilience through focused audits.'} color={'#F472B6'} />
                <ActionGrid count={getFilteredActions('Plant', ['open', 'returned']).length} title={'Plant & Equipment'} url={'/apps/equipment'} desc={'Optimize plant and equipment management for better decision-making.'} color={'#b93f10'} />
                {isReporter && <ActionGrid count={getFilteredActions('Plant', ['open', 'returned']).length} title={'EHS Statistics'} url={'/apps/reports'} desc={''} color={'#10B981'} />}

            </div>

            <div className='row'>
                {action.length !== 0 ? <ActionLog data={action} dates={date} total={total} /> : ''}
            </div>

        </>
    )
}

export default ActionList;