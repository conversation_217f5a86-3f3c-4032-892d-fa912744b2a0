// FormRender Component Styles
// Professional styling using Radix UI and project design system

// Define local variables to avoid import issues
$primary-color: #d62828;
$secondary-color: #6d3434;
$success-color: #34B1AA;
$info-color: #05C3FB;
$warning-color: #E29E09;
$danger-color: #F95F53;
$light-color: #E9E9E9;
$dark-color: #1E283D;

$success: #34B1AA;

$white-color: #ffffff;
$gray-color: #434a54;
$gray-light-color: #aab2bd;
$gray-lighter-color: #e8eff4;
$gray-lightest-color: #e6e9ed;
$gray-dark-color: #0f1531;
$black-color: #000000;

$body-text-color: #1F1F1F;
$border-color: #dee2e6;
$content-bg: #F4F5F7;
$card-title-color: #787878;

$font-family: 'Lato', sans-serif;
$font-weight-light: 300;
$font-weight-medium: 500;
$font-weight-bold: 700;

$border-radius: 8px;
$card-border-radius: 14px;

.form-render__container {
  max-width: 100%;
  margin: 0 auto;
  font-family: $font-family;
  color: $body-text-color;
}

// Checklist Section
.form-render__checklist-section {
  margin-bottom: 2rem;
  background: $white-color;
  border-radius: $card-border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-render__section-title {
  font-size: 1.25rem;
  font-weight: $font-weight-bold;
  color: $card-title-color;
  margin: 0;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid $border-color;
}

// Tabs Container
.form-render__tabs {
  width: 100%;
}

// Scrollable Tabs Area
.form-render__tabs-scroll {
  width: 100%;
  overflow: hidden;
  padding: 0 1.5rem;
  margin-top: 1rem;
}

.form-render__tabs-viewport {
  width: 100%;
  height: 100%;
}

.form-render__tabs-list {
  display: flex;
  gap: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid $border-color;
  margin-bottom: 1.5rem;
}

// Tab Triggers
.form-render__tab-trigger {
  all: unset;
  font-family: inherit;
  padding: 0.75rem 1.25rem;
  border-radius: $border-radius;
  font-size: 0.875rem;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  border: 2px solid transparent;
  position: relative;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &[data-state="active"] {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }
}

.form-render__tab-trigger--checked {
  background: linear-gradient(135deg, $success-color 0%, darken($success-color, 10%) 100%);
  color: $white-color;
  border-color: $success-color;

  &:hover {
    background: linear-gradient(135deg, darken($success-color, 5%) 0%, darken($success-color, 15%) 100%);
  }
}

.form-render__tab-trigger--unchecked {
  background: linear-gradient(135deg, $danger-color 0%, darken($danger-color, 10%) 100%);
  color: $white-color;
  border-color: $danger-color;

  &:hover {
    background: linear-gradient(135deg, darken($danger-color, 5%) 0%, darken($danger-color, 15%) 100%);
  }
}

// Scrollbar
.form-render__scrollbar {
  display: flex;
  user-select: none;
  touch-action: none;
  padding: 2px;
  background: $gray-lighter-color;
  border-radius: 4px;

  &[data-orientation="horizontal"] {
    flex-direction: column;
    height: 8px;
  }
}

.form-render__scrollbar-thumb {
  flex: 1;
  background: $gray-light-color;
  border-radius: 4px;
  position: relative;

  &:hover {
    background: $gray-color;
  }
}

// Tab Content
.form-render__tab-content {
  padding: 0 1.5rem 1.5rem;

  &:focus {
    outline: none;
  }
}

// Questions Container
.form-render__questions-container {
  width: 100%;
}

.form-render__questions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Question Cards
.form-render__question-card {
  background: $white-color;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.form-render__question-label {
  font-size: 1rem;
  font-weight: $font-weight-medium;
  color: $body-text-color;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

// Question Options
.form-render__question-options {
  margin-bottom: 1rem;
}

.form-render__selected-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: lighten($success, 45%);
  border: 1px solid lighten($success, 25%);
  border-radius: $border-radius;
  font-weight: $font-weight-medium;
  color: darken($success, 20%);
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-render__option-indicator {
  color: $success;
  font-weight: bold;
  font-size: 1rem;
}

// Remarks Section
.form-render__question-remarks {
  border-top: 1px solid $border-color;
  padding-top: 0.75rem;
}

.form-render__remarks-label {
  font-size: 0.875rem;
  font-weight: $font-weight-bold;
  color: $gray-color;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-render__remarks-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: '#fff';
  margin: 0;
  padding: 0.75rem;
  background: $gray-lightest-color;
  border-radius: $border-radius;
  border-left: 3px solid $info-color;
}

// No Inspection State
.form-render__no-inspection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: lighten($warning-color, 45%);
  border: 2px dashed lighten($warning-color, 20%);
  border-radius: $border-radius;
  margin: 1rem 0;
}

.form-render__no-inspection-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.form-render__no-inspection-text {
  font-size: 1.125rem;
  font-weight: $font-weight-medium;
  color: darken($warning-color, 30%);
  line-height: 1.4;
}

// Image Gallery
.form-render__image-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.form-render__image-item {
  width: 100%;
  height: auto;
  border-radius: $border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }
}

// Form Items
.form-render__form-item {
  background: $white-color;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 1.25rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.form-render__form-label {
  font-size: 0.875rem;
  font-weight: $font-weight-bold;
  color: $gray-color;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-render__form-value {
  font-size: 1rem;
  line-height: 1.5;
  color: '#fff';
  padding: 0.75rem;
  background: $gray-lightest-color;
  border-radius: $border-radius;
  border-left: 3px solid $primary-color;
  font-weight: $font-weight-medium;
}

// Signature Styles
.form-render__signature-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 1rem;
  background: $gray-lightest-color;
  border-radius: $border-radius;
  border: 2px dashed $border-color;
}

.form-render__signature-image {
  max-width: 200px;
  max-height: 100px;
  width: auto;
  height: auto;
  border-radius: $border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: $white-color;
  padding: 0.5rem;
}

// Tooltip Styles
.form-render__tooltip {
  background: $gray-dark-color;
  color: $white-color;
  padding: 0.5rem 0.75rem;
  border-radius: $border-radius;
  font-size: 0.75rem;
  font-weight: $font-weight-medium;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  max-width: 200px;
  text-align: center;
}

.form-render__tooltip-arrow {
  fill: $gray-dark-color;
}

// Responsive Design
@media (max-width: 768px) {
  .form-render__tabs-list {
    flex-wrap: wrap;
  }

  .form-render__tab-trigger {
    font-size: 0.8rem;
    padding: 0.6rem 1rem;
  }

  .form-render__section-title {
    font-size: 1.125rem;
    padding: 1rem 1rem 0.75rem;
  }

  .form-render__tab-content {
    padding: 0 1rem 1rem;
  }

  .form-render__question-card {
    padding: 1rem;
  }

  .form-render__no-inspection {
    padding: 2rem 1rem;
  }

  .form-render__no-inspection-icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .form-render__image-list {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .form-render__tabs-scroll {
    padding: 0 1rem;
  }
}
