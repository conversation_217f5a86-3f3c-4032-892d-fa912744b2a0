import React, { useState, useEffect } from 'react';
import HighchartsWrapper from './HighchartsWrapper';
import API from '../services/API';

const WaterChart = ({ type }) => {
  const [apiData, setApiData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      if (type === 'eptw') {
        try {
          const response = await API.post('/permits/type-distribution');
          // Transform API response: rename types
          const formattedData = response.data.permitTypeDistribution.map((item) => ({
            ...item,
            type: item.type === 'DC' ? 'Data Center' : item.type === 'CA' ? 'Construction Activity' : item.type,
          }));
          setApiData(formattedData);
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      }
    };
    fetchData();
  }, [type]);

  const staticData = [
    { name: 'Data Centre', value: 42 },
    { name: 'Construction', value: 24 },
    { name: 'Tower Crane', value: 2 },
  ];

  const alternateData = [
    { name: 'Safety', value: 25 },
    { name: 'Environment', value: 10 },
    { name: 'Health', value: 6 },
  ];

  // Determine the dataset to use
  const dataset = apiData
    ? apiData
    : type === 'eptw'
    ? staticData
    : alternateData;

  // Extract labels and values
  const labels = dataset.map((item) => item.type || item.name);
  const dataValues = dataset.map((item) => item.count || item.value);

  // Highcharts Pie Chart Configuration
  const options = {
    chart: {
      type: 'pie',
    },
    title: {
      text: ``,
    },
    tooltip: {
      pointFormat: "<b>{point.name}</b>: {point.y}",
    },
    accessibility: {
      point: {
        valueSuffix: ' permits',
      },
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        dataLabels: {
          enabled: true,
          format: "<b>{point.name}</b>: {point.y}",
        },
        showInLegend: true, // Enables legend interactivity
      },
    },
    legend: {
      enabled: true,
      labelFormatter: function () {
        return `${this.name} (${this.y})`; // Custom legend format
      },
    },
    series: [
      {
        name: 'Count',
        colorByPoint: true,
        data: labels.map((label, index) => ({
          name: label,
          y: dataValues[index],
        })),
      },
    ],
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          menuItems: [
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS',
          ],
        },
      },
    },
  };

  return (
    <div style={{ width: '100%' }}>
      <HighchartsWrapper options={options} />
    </div>
  );
};

export default WaterChart;
