import React, { useState } from 'react';
import { Tab, Nav } from 'react-bootstrap';
import { STATIC_URL } from '../constants';
import moduleStyles from './FormRender.module.scss';

const FormRender = ({ formData }) => {
    const [activeChecklistTab, setActiveChecklistTab] = useState(null);

    const styles = {
        formContainer: {

        },
        formGroup: {
            marginBottom: '15px',
        },
        label: {
            fontWeight: 'bold',
            display: 'block',
            marginBottom: '5px',
        },
        textDisplay: {
            padding: '8px',
            margin: '5px 0',
            background: '#e9ecef',
            borderRadius: '4px',
        },
        imageList: {
            listStyleType: 'none',
            paddingLeft: '0',
        },
        imageItem: {
            borderRadius: '4px',
            margin: '5px 0',
            maxWidth: '100%', // Ensure images are responsive
        }
    };

    const renderImages = (imageUrls) => {
        if (imageUrls) {
            const urls = imageUrls.split(', ');
            return (
                <ul style={styles.imageList}>
                    {urls.map((url, index) => (
                        <li key={index}>
                            <img src={`${STATIC_URL}/${url}`} alt={`Uploaded content ${index + 1}`} style={styles.imageItem} />
                        </li>
                    ))}
                </ul>
            );
        }

    };

    // Filter all checklist-group items for tab rendering
    const checklistGroups = formData.filter(item => item.type === 'checklist-group');

    // Set default active tab to first checklist group if not set
    React.useEffect(() => {
        if (checklistGroups.length > 0 && activeChecklistTab === null) {
            setActiveChecklistTab(`checklist-${0}`);
        }
    }, [checklistGroups.length, activeChecklistTab]);

    return (
        <div style={styles.formContainer}>
            {/* Render checklist groups as tabs if any exist */}
            {checklistGroups.length > 0 && (
                <div className={`${moduleStyles.checklistTabsContainer} mt-4 mb-4`}>
                    <Tab.Container
                        id="checklist-tabs"
                        activeKey={activeChecklistTab}
                        onSelect={(k) => setActiveChecklistTab(k)}
                    >
                        {/* Horizontal scrollable tab navigation */}
                        <div className={moduleStyles.tabNavContainer}>
                            <Nav variant="pills" className={`${moduleStyles.horizontalScrollTabs} flex-nowrap`}>
                                {checklistGroups.map((item, index) => {
                                    const label = item.label || 'No Label';
                                    const isChecked = item.checked;

                                    return (
                                        <Nav.Item key={index} className={moduleStyles.tabNavItem}>
                                            <Nav.Link
                                                eventKey={`checklist-${index}`}
                                                className={`${moduleStyles.tabNavLink} ${!isChecked ? moduleStyles.uncheckedTab : ''}`}
                                                dangerouslySetInnerHTML={{ __html: label }}
                                            />
                                            {!isChecked && (
                                                <div className={moduleStyles.uncheckedIndicator}>!</div>
                                            )}
                                        </Nav.Item>
                                    );
                                })}
                            </Nav>
                        </div>

                        {/* Tab content */}
                        <Tab.Content className={moduleStyles.tabContent}>
                            {checklistGroups.map((item, index) => {
                                const questions = item.questions || [];
                                const isChecked = item.checked;

                                return (
                                    <Tab.Pane key={index} eventKey={`checklist-${index}`}>
                                        {isChecked ? (
                                            // Show questions if checked
                                            questions.length > 0 && (
                                                <div>
                                                    {questions.map((question, qIndex) => {
                                                        const questionLabel = question.label || 'No Question Label';
                                                        const options = question.options || [];

                                                        return (
                                                            <div key={qIndex} className='boxShadow p-2 mt-3'>
                                                                <div>Q . {questionLabel}</div>
                                                                {options.length > 0 && (
                                                                    <div className='mt-3'>
                                                                        {options.map((option, oIndex) => {
                                                                            if (option.checked === 1) {
                                                                                return (
                                                                                    <div key={oIndex} className='fw-bold'>
                                                                                        {option.label}
                                                                                    </div>
                                                                                );
                                                                            } else {
                                                                                return null;
                                                                            }
                                                                        })}
                                                                    </div>
                                                                )}

                                                                <div className='fw-bold mt-2'>Remarks</div>
                                                                <p>{question.remarks}</p>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            )
                                        ) : (
                                            // Show "No Inspection" message if not checked
                                            <div className={`${moduleStyles.noInspectionMessage} text-center p-4`}>
                                                <div className={moduleStyles.noInspectionIcon}>⚠️</div>
                                                <h5 className="text-danger mt-2">No Inspection carried out in this section</h5>
                                            </div>
                                        )}
                                    </Tab.Pane>
                                );
                            })}
                        </Tab.Content>
                    </Tab.Container>
                </div>
            )}

            {/* Render other form items */}
            {formData.map((item, index) => {
                if (item.type === 'checklist-group') {
                    // Skip checklist groups as they are now rendered as tabs above
                    return null;
                } else if (item.type === "textarea") {

                    return (
                        <div className='boxShadow mt-3 mb-3 p-2'>

                            <div>{item.label}</div>
                            <div className='fw-bold mt-2'>{item.value || ''}</div>
                        </div>
                    )
                } else if (item.type === "sign") {

                    return (
                        <div className='boxShadow mt-2 mb-3 p-2 '>
                            <div>{item.label}</div>
                            <img src={`${STATIC_URL}/${item.value}`} width={100} alt="Signature" />

                        </div>

                    )
                }
                return null; // Add explicit return for other types
            })}
        </div>

    );
};

export default FormRender;
