import React, { useState, useEffect } from "react";
import { Row, Col, Card, Form, Modal } from "react-bootstrap";
import "react-datepicker/dist/react-datepicker.css";
import {
    DYNAMIC_TITLES_URL,
    LOCATION1_URL,
    LOCATION_TWO,
    GET_USERS_BY_ROLE,
    CHECKLIST_URL,
    LOCATION_THREE,
    LOCATION_FOUR,
    LOCATION_FIVE,
    LOCATION_SIX,
    INSPECTION_URL,
} from "../../constants";
import API from "../../services/API";
import SelectCardComponent from "../Eptw/component/SelectCardComponent";
import Swal from "sweetalert2";
import Select from "react-select";
import { useSelector } from "react-redux";
import moment from "moment";
import FullLoader from "../Eptw/component/FullLoader";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const AddInspection = ({ show, handleClose, inspectionData }) => {
    const initialFormState = {
        checklistId: "",
        startDate: null,
        startDateTime: null,
        dueDate: null,
        selectedInspector: "",
        locOne: "",
        locTwo: "",
        locThree: "",
        locFour: "",
        locFive: "",
        locSix: "",
    };

    const [formState, setFormState] = useState(initialFormState);
    const [formErrors, setFormErrors] = useState({});

    const user = useSelector((state) => state.login.user);
    const [title, setTitle] = useState([]);
    const [locationOne, setLocationOne] = useState([]);
    const [locationTwo, setLocationTwo] = useState([]);
    const [locationThree, setLocationThree] = useState([]);
    const [locationFour, setLocationFour] = useState([]);
    const [locationFive, setLocationFive] = useState([]);
    const [locationSix, setLocationSix] = useState([]);
    const [locationSearchOne, setLocationSearchOne] = useState([]);
    const [locationSearchTwo, setLocationSearchTwo] = useState([]);
    const [locationSearchThree, setLocationSearchThree] = useState([]);
    const [locationSearchFour, setLocationSearchFour] = useState([]);
    const [locationSearchFive, setLocationSearchFive] = useState([]);
    const [locationSearchSix, setLocationSearchSix] = useState([]);
    const [checklist, setChecklist] = useState([]);
    const [loader, setLoader] = useState(false);
    const [inspector, setInspector] = useState([]);

    useEffect(() => {
        getTitle();
        getLocationOne();
        getChecklist();
    }, []);

    useEffect(() => {
        if (inspectionData && Object.keys(inspectionData).length > 0) {
            // Pre-fill data for editing
            setFormState({
                checklistId: {
                    label: inspectionData.checklist?.name,
                    value: inspectionData.checklistId,
                },
                startDate: inspectionData.startDateTime ? moment(inspectionData.startDateTime, "DD-MM-YYYY").toDate() : null,
                startDateTime: inspectionData.startDateTime ? moment(inspectionData.startDateTime, "DD-MM-YYYY").toDate() : null,
                dueDate: inspectionData.dateTime
                    ? moment(inspectionData.dateTime, "DD-MM-YYYY").toDate()
                    : null,
                selectedInspector: {
                    label: inspectionData.assignedTo?.firstName,
                    value: inspectionData.assignedToId,
                },
                locOne: {
                    name: inspectionData.locationOne?.name,
                    id: inspectionData.locationOneId,
                },
                locTwo: {
                    name: inspectionData.locationTwo?.name,
                    id: inspectionData.locationTwoId,
                },
                locThree: {
                    name: inspectionData.locationThree?.name,
                    id: inspectionData.locationThreeId,
                },
                locFour: {
                    name: inspectionData.locationFour?.name,
                    id: inspectionData.locationFourId,
                },
                // Reset locFive and locSix if they are not used
                locFive: "",
                locSix: "",
            });

            // After setting locations, fetch inspectors
            const reqData = {
                locationOneId: inspectionData.locationOneId,
                locationTwoId: inspectionData.locationTwoId,
                locationThreeId: inspectionData.locationThreeId,
                locationFourId: inspectionData.locationFourId,
                mode: "inspector",
            };
            getUsersByRole(reqData);
        } else {
            // Reset form fields when adding a new inspection
            setFormState(initialFormState);
            setInspector([]);
        }
    }, [inspectionData]);

    // Update inspector list when locations change
    useEffect(() => {
        const requiredFields = [
            formState.locOne,
            formState.locTwo,
            formState.locThree,
            formState.locFour,
        ];
        if (
            requiredFields.every(
                (obj) => obj && obj.hasOwnProperty("id") && obj.id !== undefined
            )
        ) {
            setFormState((prevState) => ({
                ...prevState,
                selectedInspector: "",
            }));
            const reqData = {
                locationOneId: formState.locOne.id,
                locationTwoId: formState.locTwo.id,
                locationThreeId: formState.locThree.id,
                locationFourId: formState.locFour.id,
                mode: "inspector",
            };
            getUsersByRole(reqData);
        }
    }, [
        formState.locOne,
        formState.locTwo,
        formState.locThree,
        formState.locFour,
    ]);

    const handleInputChange = (field, value) => {
        // If editing an inspection and trying to change location or checklist, prevent the change
        if (inspectionData && Object.keys(inspectionData).length > 0) {
            // List of fields that should be read-only when editing
            const readOnlyFields = ['locOne', 'locTwo', 'locThree', 'locFour', 'locFive', 'locSix', 'checklistId'];

            // If the field is in the read-only list, don't update it
            if (readOnlyFields.includes(field)) {
                return;
            }
        }

        setFormState((prevState) => {
            // If startDate changes, also reset dueDate
            if (field === "startDate") {
                return {
                    ...prevState,
                    startDate: value,
                    dueDate: null, // reset due date
                };
            }

            return {
                ...prevState,
                [field]: value,
            };
        });

        // Clear the error for this field
        setFormErrors((prevErrors) => ({
            ...prevErrors,
            [field]: undefined,
        }));
    };

    const getTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        if (response.status === 200) {
            setTitle(response.data);
        }
    };

    const getLocationOne = async () => {
        const response = await API.get(LOCATION1_URL);
        if (response.status === 200) {
            setLocationOne(response.data);
            setLocationSearchOne(response.data);
        }
    };

    const getChecklist = async () => {
        const response = await API.get(CHECKLIST_URL);
        if (response.status === 200) {
            const data = response.data
                .filter((i) => i.application === "Inspection")
                .map((i) => ({ label: i.name, value: i.id }));
            setChecklist(data);
        }
    };

    const checkRequiredFields = () => {
        const errors = {};

        if (!formState.locOne || !formState.locOne.id) {
            errors.locOne = "Location One is required";
        }
        if (!formState.locTwo || !formState.locTwo.id) {
            errors.locTwo = "Location Two is required";
        }
        if (!formState.locThree || !formState.locThree.id) {
            errors.locThree = "Location Three is required";
        }
        if (!formState.locFour || !formState.locFour.id) {
            errors.locFour = "Location Four is required";
        }
        if (!formState.checklistId) {
            errors.checklistId = "Checklist is required";
        }
        if (!formState.startDate) {
            errors.startDate = "Start Date is required";
        }
        if (!formState.dueDate) {
            errors.dueDate = "Due Date is required";
        }
        if (!formState.selectedInspector) {
            errors.selectedInspector = "Inspector is required";
        }

        setFormErrors(errors);

        if (Object.keys(errors).length > 0) {
            Swal.fire("Please fill all the required fields");
            return true;
        }
        return false;
    };

    const onsubmit = async () => {
        setLoader(true);
        if (checkRequiredFields()) {
            setLoader(false);
            return;
        }
        submitDcOps();
    };

    const submitDcOps = async () => {
        setLoader(true);

        try {
            // Prepare request data
            let reqData = {
                locationOneId: formState.locOne.id,
                locationTwoId: formState.locTwo.id,
                locationThreeId: formState.locThree.id,
                locationFourId: formState.locFour.id,
                checklistId: typeof formState.checklistId === 'object' ? formState.checklistId.value : formState.checklistId,
                assignedToId: typeof formState.selectedInspector === 'object' ? formState.selectedInspector.value : formState.selectedInspector,
                startDateTime: moment(formState.startDate).format("DD-MM-YYYY"),
                dateTime: moment(formState.dueDate).format("DD-MM-YYYY"),
                month: moment(formState.dueDate).format("MM"),
                year: moment(formState.dueDate).format("YYYY"),
            };

            let response;
            if (inspectionData && Object.keys(inspectionData).length > 0) {
                // If editing, do a PUT request
                response = await API.patch(
                    `${INSPECTION_URL}/${inspectionData.id}`,
                    JSON.stringify(reqData)
                );
            } else {
                // If adding, do a POST request
                response = await API.post(INSPECTION_URL, JSON.stringify(reqData));
            }

            const isSuccess = (inspectionData && Object.keys(inspectionData).length > 0)
                ? response.status === 204
                : response.status === 200;

            if (isSuccess) {
                setLoader(false);
                Swal.fire({
                    title: "",
                    text: `This Inspection has been ${inspectionData ? "updated" : "scheduled"}.`,
                    icon: "success",
                    confirmButtonText: "Ok",
                }).then((result) => {
                    if (result.isConfirmed) {
                        handleClose(false); // Close the modal on confirmation
                        window.location.reload(true)
                    }
                });
            } else {
                setLoader(false);
                Swal.fire({
                    title: "",
                    text: "Please Try Again.",
                    icon: "warning",
                    confirmButtonText: "Yes",
                }).then((result) => {
                    if (result.isConfirmed) {
                        handleClose(false);
                    }
                });
            }
        } catch (error) {
            setLoader(false);
            console.error("Error submitting inspection:", error);
            Swal.fire({
                title: "Error",
                text: "There was an issue submitting the inspection. Please try again.",
                icon: "error",
                confirmButtonText: "Ok",
            });
        }
    };

    const updateSelected = async (item, type) => {
        // If editing an inspection, prevent location changes
        if (inspectionData && Object.keys(inspectionData).length > 0) {
            return; // Don't allow location changes when editing
        }

        if (type === "one") {
            handleInputChange("locOne", item);
            handleInputChange("locTwo", "");
            handleInputChange("locThree", "");
            handleInputChange("locFour", "");
            handleInputChange("locFive", "");
            handleInputChange("locSix", "");
            const response = await API.get(LOCATION_TWO(item.id));
            if (response.status === 200) {
                setLocationTwo(response.data);
                setLocationSearchTwo(response.data);
            }
        } else if (type === "two") {
            handleInputChange("locTwo", item);
            handleInputChange("locThree", "");
            handleInputChange("locFour", "");
            handleInputChange("locFive", "");
            handleInputChange("locSix", "");
            const response = await API.get(LOCATION_THREE(item.id));
            if (response.status === 200) {
                setLocationThree(response.data);
                setLocationSearchThree(response.data);
            }
        } else if (type === "three") {
            handleInputChange("locThree", item);
            handleInputChange("locFour", "");
            handleInputChange("locFive", "");
            handleInputChange("locSix", "");
            const response = await API.get(LOCATION_FOUR(item.id));
            if (response.status === 200) {
                setLocationFour(response.data);
                setLocationSearchFour(response.data);
            }
        } else if (type === "four") {
            handleInputChange("locFour", item);
            handleInputChange("locFive", "");
            handleInputChange("locSix", "");
            const response = await API.get(LOCATION_FIVE(item.id));
            if (response.status === 200) {
                setLocationFive(response.data);
                setLocationSearchFive(response.data);
            }
        } else if (type === "five") {
            handleInputChange("locFive", item);
            handleInputChange("locSix", "");
            const response = await API.get(LOCATION_SIX(item.id));
            if (response.status === 200) {
                setLocationSix(response.data);
                setLocationSearchSix(response.data);
            }
        } else if (type === "six") {
            handleInputChange("locSix", item);
        }
    };

    const searchValue = (name, type) => {
        // If editing an inspection, prevent location searches
        if (inspectionData && Object.keys(inspectionData).length > 0) {
            return; // Don't allow location searches when editing
        }

        if (type === "one") {
            const one = locationSearchOne;
            setLocationOne(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        } else if (type === "two") {
            const one = locationSearchTwo;
            setLocationTwo(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        } else if (type === "three") {
            const one = locationSearchThree;
            setLocationThree(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        } else if (type === "four") {
            const one = locationSearchFour;
            setLocationFour(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        } else if (type === "five") {
            const one = locationSearchFive;
            setLocationFive(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        } else if (type === "six") {
            const one = locationSearchSix;
            setLocationSix(
                one.filter((i) =>
                    i.name.toLowerCase().includes(name.toLowerCase())
                )
            );
        }
    };

    const getUsersByRole = async (data) => {
        const response = await API.post(GET_USERS_BY_ROLE, data);
        if (response.status === 200) {
            const data = response.data.map((i) => ({
                label: i.firstName,
                value: i.id,
            }));
            setInspector(data);

            // If editing, set the selectedInspector if it matches one of the options
            if (
                inspectionData &&
                Object.keys(inspectionData).length > 0 &&
                inspectionData.assignedToId
            ) {
                const match = data.find(
                    (option) => option.value === inspectionData.assignedToId
                );
                if (match) {
                    setFormState((prevState) => ({
                        ...prevState,
                        selectedInspector: match,
                    }));
                } else {
                    setFormState((prevState) => ({
                        ...prevState,
                        selectedInspector: "",
                    }));
                }
            }
        }
    };

    return (
        <>
            <Modal show={show} onHide={() => handleClose(false)} size="md">
                <Modal.Header closeButton>
                    <Modal.Title>
                        {inspectionData && Object.keys(inspectionData).length > 0
                            ? "Edit"
                            : "Assign"}{" "}
                        Checklist
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row>
                        <Row>
                            <Col md={12}>
                                {inspectionData && Object.keys(inspectionData).length > 0 ? (
                                    <Card>
                                        <Card.Body>
                                            <strong>Location:</strong>{" "}
                                            {formState.locOne?.name} &gt; {formState.locTwo?.name} &gt; {formState.locThree?.name} &gt; {formState.locFour?.name}
                                        </Card.Body>
                                    </Card>
                                ) : (<>
                                    <SelectCardComponent
                                        mandatory={true}
                                        title={title.length !== 0 ? title[0].altTitle : ""}
                                        data={locationOne}
                                        selectedValue={formState.locOne.id}
                                        label={
                                            formState.locOne === "" ? "Select" : formState.locOne.name
                                        }
                                        updateListSelected={updateSelected}
                                        type={"one"}
                                        searchList={searchValue}
                                        box={false}
                                        error={formErrors.locOne}
                                    />
                                    <SelectCardComponent
                                        mandatory={true}
                                        title={title.length !== 0 ? title[1].altTitle : ""}
                                        data={locationTwo}
                                        selectedValue={formState.locTwo.id}
                                        label={
                                            formState.locTwo === "" ? "Select" : formState.locTwo.name
                                        }
                                        updateListSelected={updateSelected}
                                        type={"two"}
                                        searchList={searchValue}
                                        box={false}
                                        error={formErrors.locTwo}
                                    />
                                    <SelectCardComponent
                                        mandatory={true}
                                        title={title.length !== 0 ? title[2].altTitle : ""}
                                        data={locationThree}
                                        selectedValue={formState.locThree.id}
                                        label={
                                            formState.locThree === ""
                                                ? "Select"
                                                : formState.locThree.name
                                        }
                                        updateListSelected={updateSelected}
                                        type={"three"}
                                        searchList={searchValue}
                                        box={false}
                                        error={formErrors.locThree}
                                    />
                                    <SelectCardComponent
                                        mandatory={true}
                                        title={title.length !== 0 ? title[3].altTitle : ""}
                                        data={locationFour}
                                        selectedValue={formState.locFour.id}
                                        label={
                                            formState.locFour === ""
                                                ? "Select"
                                                : formState.locFour.name
                                        }
                                        updateListSelected={updateSelected}
                                        type={"four"}
                                        searchList={searchValue}
                                        box={false}
                                        error={formErrors.locFour}
                                    />
                                </>)}
                            </Col>

                        </Row>

                        <Row>
                            <Col className="m-auto" xs={12} sm={12} md={12}>
                                <Card>
                                    <Card.Body className="pb-0">
                                        <Form.Group controlId="formSelect">

                                            {inspectionData && Object.keys(inspectionData).length > 0 ? (<>
                                                <Col xs={12} className="mb-3 d-flex justify-content-between">
                                                    <div><strong>Start Date:</strong> {formState.startDate ? moment(formState.startDate).format("DD-MM-YYYY") : "-"}</div>
                                                    <div><strong>Due Date:</strong> {formState.dueDate ? moment(formState.dueDate).format("DD-MM-YYYY") : "-"}</div>
                                                </Col>

                                                <Col xs={12} className="mb-3">
                                                    <strong>Checklist:</strong>  {formState.checklistId?.label || "-"}
                                                </Col>
                                            </>) : (<>
                                                <Form.Label>
                                                    Checklist{" "}
                                                    <span style={{ color: "#D62828" }}>*</span>
                                                </Form.Label>

                                                <Select
                                                    value={formState.checklistId}
                                                    onChange={(e) => handleInputChange("checklistId", e)}
                                                    options={checklist}
                                                    placeholder={"Select..."}
                                                    isSearchable
                                                    styles={{
                                                        control: (base) => ({
                                                            ...base,
                                                            borderColor: formErrors.checklistId ? "red" : "#dee2e6",
                                                            borderRadius: "10px",
                                                            boxShadow: "none",
                                                            "&:hover": { borderColor: "#ced4da" },
                                                        }),
                                                    }}
                                                />
                                            </>)}

                                        </Form.Group>
                                    </Card.Body>
                                </Card>
                            </Col>
                            {/* <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card>
                                    <Card.Body>
                                        <Form.Group controlId="formStartDate">

                                            {inspectionData && Object.keys(inspectionData).length > 0 ? (<></>
                                                // <div className="form-control" style={{ borderRadius: "10px" }}>
                                                //     {formState.startDate ? moment(formState.startDate).format("DD-MM-YYYY") : ""}
                                                // </div>
                                            ) : (<>
                                                <Form.Label>
                                                    Start Date <span style={{ color: "#D62828" }}>*</span>
                                                </Form.Label>

                                                <DatePicker
                                                    selected={formState.startDate}
                                                    onChange={(date) => handleInputChange("startDate", date)}
                                                    dateFormat="dd-MM-yyyy"
                                                    className="form-control"
                                                    minDate={new Date()}
                                                    placeholderText="Select Start Date"
                                                />
                                            </>)}
                                            {formErrors.startDate && (
                                                <div style={{ color: "#D62828", fontSize: "0.8em" }}>{formErrors.startDate}</div>
                                            )}
                                        </Form.Group>
                                    </Card.Body>
                                </Card>
                            </Col>
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card>
                                    <Card.Body>
                                        <Form.Group controlId="formDueDate">

                                            {inspectionData && Object.keys(inspectionData).length > 0 ? (<></>
                                                // <div className="form-control" style={{ borderRadius: "10px" }}>
                                                //     {formState.dueDate ? moment(formState.dueDate).format("DD-MM-YYYY") : ""}
                                                // </div>
                                            ) : (<>
                                                <Form.Label>
                                                    Due Date{" "}
                                                    <span style={{ color: "#D62828" }}>*</span>
                                                </Form.Label>

                                                <DatePicker
                                                    selected={formState.dueDate}
                                                    onChange={(date) =>
                                                        handleInputChange("dueDate", date)
                                                    }
                                                    placeholderText="Select Due Date"
                                                    minDate={formState.startDate && new Date(formState.startDate)}
                                                    className={`form-control ${formErrors.dueDate ? "is-invalid" : ""
                                                        }`}
                                                    dateFormat="dd-MM-yyyy"
                                                />
                                            </>)}

                                        </Form.Group>
                                    </Card.Body>
                                </Card>
                            </Col> */}
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card>
                                    <Card.Body>
                                        {inspectionData && Object.keys(inspectionData).length > 0 && (
                                            <>
                                                <Form.Label>

                                                    <span style={{ color: "#D62828" }}>* You can only reschedule the assigned Inspector. Inspections need to be completed as planned. For exigencies, please reach out to the SuperAdmin for deletion of scheduled inspections.</span>
                                                </Form.Label>

                                            </>
                                        )}
                                        <Form.Group controlId="formSelect">
                                            <Form.Label>
                                                Inspector{" "}
                                                <span style={{ color: "#D62828" }}>*</span>
                                            </Form.Label>
                                            <Select
                                                value={formState.selectedInspector}
                                                onChange={(e) =>
                                                    handleInputChange("selectedInspector", e)
                                                }
                                                options={inspector}
                                                placeholder={"Select..."}
                                                isSearchable
                                                styles={{
                                                    control: (base) => ({
                                                        ...base,
                                                        borderColor: formErrors.selectedInspector
                                                            ? "red"
                                                            : "#dee2e6",
                                                        borderRadius: "10px",
                                                        boxShadow: "none",
                                                        "&:hover": { borderColor: "#ced4da" },
                                                    }),
                                                }}
                                            />

                                        </Form.Group>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    </Row>
                    {loader && <FullLoader />}
                </Modal.Body>
                <Modal.Footer>
                    <button className="btn btn-light" onClick={() => handleClose(false)}>Close</button>
                    {inspectionData && Object.keys(inspectionData).length > 0 ? (
                        <button className="btn btn-primary" onClick={() => onsubmit()}>Update</button>
                    ) : (
                        !inspectionData ? (
                            <button className="btn btn-danger" onClick={() => onsubmit()}>Assign</button>
                        ) : (
                            <button className="btn btn-primary" onClick={() => onsubmit()}>Submit</button>
                        )
                    )}
                </Modal.Footer>
            </Modal>
            {loader && <FullLoader />}
        </>
    );
};

export default AddInspection;
