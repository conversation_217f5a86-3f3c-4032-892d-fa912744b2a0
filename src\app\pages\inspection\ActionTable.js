import React, { useState, useEffect, useMemo } from 'react';
// import moment from 'moment';
import { USERS_URL, STATIC_URL, GET_USERS_BY_ROLE, INSPECTION_CONTROL_MEASURE_ID, API_URL } from '../../constants';
import API from '../../services/API';
import { Accordion, AccordionTab } from 'primereact/accordion';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import GalleryPage from '../../apps/Gallery';
import Typography from '@mui/material/Typography'
import moment from 'moment-timezone';
import { Button, Modal } from 'react-bootstrap';
import Select from 'react-select';
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
import PdfIcon from '@material-ui/icons/PictureAsPdf';
import DescriptionIcon from '@material-ui/icons/Description';
import { DropzoneArea } from 'material-ui-dropzone';
import axios from 'axios';
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css';
const customFontStyle = {
    fontFamily: 'Lato, sans-serif',

};
const ActionTable = ({ id, actions, current }) => {
    const data = useSelector((state) => state.login.user);

    console.log(current)
    console.log(actions)
    const [users, setUsers] = useState([]);
    const [totalAction, setTotalAction] = useState([])

    const [showModal, setShowModal] = useState(false)
    const [readOnly, setReadOnly] = useState(false)
    const [controlMeasureFiles, setControlMeasureFiles] = useState({});


    const [locationUsers, setLocationUsers] = useState([])
    const validationRoles = data?.validationRoles || [];
    const [controlMeasures, setControlMeasures] = useState([]);
    const [riskAssessments, setRiskAssessments] = useState([]);
    const [validationErrors, setValidationErrors] = useState([]);
    const [validationControlErrors, setValidationControlErrors] = useState([]);
    const [files, setFiles] = useState([])

    let cm = 0.0;
    let doc = 0.0;
    let picm = 0.0;
    let nc = 0.0

    let rass = 0.0;
    let cmss = 0;
    let poss = 0.0
    let ncs = 0.0

    useEffect(() => {
        getAllUsers();

        setTotalAction(actions); // Log totalActions for debugging or potential use
    }, []);

    useEffect(() => {
        const fetchUsers = async () => {
            try {

                const result = await API.post(GET_USERS_BY_ROLE, { locationOneId: current.locationOneId, locationTwoId: current.locationTwoId, locationThreeId: current.locationThreeId, locationFourId: current.locationFourId, mode: 'inspection-action-plan-implementor' });
                setLocationUsers(result.data.map(user => ({
                    value: user.id,
                    label: user.firstName
                })));
            } catch (error) {
                console.error('Error fetching data', error);
            }
        };
        if (current.locationOneId && current.locationTwoId && current.locationThreeId && current.locationFourId)
            fetchUsers();

    }, [current])


    const convertToLocalTime = (gmtDate) => {
        // Get the system's time zone
        const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Define formats to handle
        const customFormat = 'DD-MM-YYYY HH:mm'; // Format for "23-07-2024 13:35"

        let localDate;

        if (moment(gmtDate, customFormat, true).isValid()) {
            // If the input matches the custom format
            localDate = moment.tz(gmtDate, customFormat, 'GMT').tz(systemTimeZone).format('Do MMM YYYY, hh:mm A');
        } else if (moment(gmtDate).isValid()) {
            // If the input is a valid ISO 8601 date
            localDate = moment.tz(gmtDate, 'GMT').tz(systemTimeZone).format('Do MMM YYYY, hh:mm A');
        } else {
            throw new Error('Invalid date format');
        }

        return localDate;
    };


    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data);
    };

    const getName = (id) => {
        const user = users.find(user => user.id === id);
        return user?.firstName || '';
    };

    const getActioNumber = (action, index) => {

        if (index === 'cm') {

            cmss = cmss + 1
            let status = ''
            action.data.map(item => {
                if (item.actionType === 'ins_take_actions_control') {
                    status = 'IA  -' + cmss
                } else if (item.actionType === 'ins_retake_actions') {
                    status = 'IA -' + (cmss + 0.1)
                }
            })
            return status
        }
        // if (index === 'ra') {
        //     rass = rass + 1.0
        //     let status = ''
        //     action.data.map(item => {
        //         if (item.actionType === 'take_actions_ra') {
        //             status = 'DOC ' + rass.toFixed(1)
        //         } else if (item.actionType === 'retake_actions') {
        //             status = 'DOC ' + (rass + 0.1)
        //         }
        //     })
        //     return status
        // }
        // if (index === 'picm') {
        //     poss = poss + 1.0
        //     let status = ''
        //     action.data.map(item => {
        //         if (item.actionType === 'take_actions_control_post') {
        //             status = 'PICM ' + poss.toFixed(1)
        //         } else if (item.actionType === 'retake_actions') {
        //             status = 'PICM ' + (poss + 0.1)
        //         }
        //     })
        //     return status
        // }
        // if (index === 'nc') {
        //     ncs = ncs + 1.0
        //     let status = ''
        //     action.data.map(item => {
        //         if (item.actionType === 'audit_take_actions') {
        //             status = 'NC ' + ncs.toFixed(1)
        //         } else if (item.actionType === 'retake_actions') {
        //             status = 'NC ' + (ncs + 0.1)
        //         }
        //     })
        //     return status
        // }

    }
    const getStatusAction = (action, index) => {

        let status = '';
        let ras = false;
        let cms = false;
        let pos = false;
        let ncs = false


        if (action.firstActionType === 'ins_take_actions_control') {

            switch (action.lastActionType) {
                case 'ins_take_actions_control':
                    status = ' Assigned'

                    break;
                case 'ins_retake_actions':
                    status = 'Re- Assigned'

                    break
                case 'ins_verify_actions':
                    status = 'Implemented - Pending Verification'
                    break

                case 'approve':
                    status = 'Verified & Closed'
                    break
                default:

                    break;
            }

            cms = true
        }
        // else if (action.firstActionType === 'take_actions_ra') {
        //     switch (action.lastActionType) {
        //         case 'take_actions_ra':
        //             status = 'RA / SWP changes assigned'
        //             break;
        //         case 'retake_actions':
        //             status = 'RA / SWP Re-changes assigned'
        //             break
        //         case 'verify_actions':
        //             status = 'RA / SWP chages done - Pending Verification'
        //             break

        //         case 'approve':
        //             status = 'RA / SWP changes: Verified & Closed'
        //             break
        //     }
        //     ras = true;
        // }
        // else if (action.firstActionType === 'take_actions_control_post') {
        //     switch (action.lastActionType) {
        //         case 'take_actions_control_post':
        //             status = 'Post Investigation Control Measures Assigned'
        //             break;
        //         case 'retake_actions':
        //             status = 'Re-Post Investigation Control Measures Assigned'
        //             break
        //         case 'verify_actions':
        //             status = 'Post Investigation Control Measures - Pending Verification'
        //             break
        //         case 'approve':
        //             status = 'Post Investigation Control Measure: Verified & Closed'
        //             break
        //     }
        //     pos = true
        // }
        // else if (action.firstActionType === 'audit_take_actions') {
        //     switch (action.lastActionType) {
        //         case 'audit_take_actions':
        //             status = 'Audit Action Assigned'
        //             break;
        //         case 'aud_retake_actions':
        //             status = 'Re-Audit Action Assigned'
        //             break
        //         case 'aud_verify_actions':
        //             status = 'Audit Action - Pending Verification'
        //             break
        //         case 'approve':
        //             status = 'Audit Action: Verified & Closed'
        //             break
        //     }
        //     cms = true
        // }



        return (

            <Typography variant="body1" style={customFontStyle}>
                <div className='row d-flex mb-2'>

                    <div className='col-6'>

                        <h4>{current.maskId} - {getActioNumber(action, index)}</h4>


                    </div>
                    <div className='col-6'>
                        <span className={`badge fw-bold ${ras ? 'status-tag-orange' : cms ? 'status-tag-blue' : pos ? 'status-tag-pink' : ''}`}>
                            {status}
                        </span>
                    </div>
                </div>
                <div className='row d-flex' >

                    <div className='col-6'>


                        {showUserByStatus(action)}


                    </div>
                    <div className='col-6'>
                        {action.data.find(item => item.actionType === 'ins_retake_actions')?.dueDate ? <>
                            <h6>DueDate : {moment(action.data.find(item => item.actionType === 'ins_retake_actions')?.dueDate, ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD']).format('Do MMM YYYY')}</h6>
                        </> :
                            <h6>DueDate : {moment(action.data[0].dueDate, ['DD/MM/YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD']).format('Do MMM YYYY')}</h6>
                        }
                    </div>
                </div>

            </Typography>
        )


    }
    const showUserByStatus = (action) => {


        switch (action.lastActionType) {
            case "ins_take_actions_control":
            case "take_actions_ra":
            case "audit_take_actions":
            case "take_actions_control_post":
                return `Action Assignee : ${getName(action.lastAssignedToId)}`;
            case "ins_verify_actions":
                return `Action to be verified by : ${getName(action.lastAssignedToId)}`;
            case "aud_verify_actions":
                return `Action to be verified by : ${getName(action.lastAssignedToId)}`;
            case "ins_retake_actions":
                return `Action Re-assigned to : ${getName(action.lastAssignedToId)}`;
            case "aud_retake_actions":
                return `Action Re-assigned to : ${getName(action.lastAssignedToId)}`;
            case "approve":
                return `Action Verified By : ${getName(action.lastAssignedToId)}`;
            default:
                return ''; // Handle default case if needed
        }
    };
    const handleFileChange = (index, files) => {
        setControlMeasureFiles(prev => ({
            ...prev,
            [index]: files
        }));
    };

    const getCMData = (data, cm, index) => {

        return data.data.map((item, index) => {
            if (item.actionType === "ins_take_actions_control") {
                // cm = cm + 1.0

                return (
                    <div className="obs-section p-4">
                        <div key={index} className="row ">
                            <div className='col-8'>
                                <p className="obs-title"> Assigned Action - IA {cm.toFixed(1)} </p>
                                <p className="obs-content">{item.actionToBeTaken}</p>
                            </div>
                            <div className='row'>


                                <div className="col-md-12">
                                    {data.firstActionType !== data.lastActionType && <>

                                        {current.postActions && current.postActions?.length > 0 && (
                                            <div className='d-flex'>
                                                <div className='col-12'>
                                                    <p className="obs-title">Images</p>
                                                    <div className='row'>
                                                        {current.postActions.find(item1 => item1.actionToBeTaken === item.actionToBeTaken)?.uploads?.map((upload) => {
                                                            const fileExtension = upload.split('.').pop().toLowerCase();

                                                            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                                // Handle image files using GalleryPage
                                                                return (

                                                                    <GalleryPage
                                                                        photos={[{
                                                                            src: `${STATIC_URL}/${upload}`,
                                                                            width: 4,
                                                                            height: 3
                                                                        }]}
                                                                        key={upload} // Use upload as key for uniqueness
                                                                    />
                                                                );
                                                            } else if (fileExtension === 'pdf') {
                                                                // Handle PDF files
                                                                return (
                                                                    <div className="col-md-3" key={upload}>
                                                                        <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                            View PDF
                                                                        </a>
                                                                    </div>
                                                                );
                                                            } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                                // Handle Excel files
                                                                return (
                                                                    <div className="col-md-3" key={upload}>
                                                                        <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                            Download Excel File
                                                                        </a>
                                                                    </div>
                                                                );
                                                            } else {
                                                                // Handle other file types
                                                                return (
                                                                    <div className="col-md-3" key={upload}>
                                                                        <p>Unsupported file type: {fileExtension}</p>
                                                                    </div>
                                                                );
                                                            }
                                                        })}
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </>}
                                </div>
                            </div>
                            {item.status === 'open' && <>
                                <p className="obs-title"> Action Assignee</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </>}
                            {item.status === 'submitted' && <>
                                <div className="row mb-3">
                                    <div className="col-md-12">
                                        <p className="obs-title">Action Taken </p>
                                        <p className="obs-content">{item.actionTaken}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Action Taken By</p>
                                        <p className="obs-content">{item.assignedToId &&
                                            getName(item.assignedToId
                                            )}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Date</p>
                                        <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>

                                        <div className='col-6'>
                                            <p className="obs-title">Due Date</p>
                                            <p className="obs-content">{moment(item.dueDate).format('Do MMM YYYY')}</p>
                                        </div>
                                    </div>
                                </div>

                            </>
                            }
                        </div>
                        <div className='row'>


                            <div className="col-md-12">

                                {item.uploads && item.uploads.length > 0 && (
                                    <div className='d-flex'>
                                        <div className='col-12'>
                                            <p className="obs-title">{data.firstActionType === data.lastActionType ? 'Images' : 'Evidence'}</p>
                                            <div className='row'>
                                                {item.uploads.map((upload) => {
                                                    const fileExtension = upload.split('.').pop().toLowerCase();

                                                    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                        // Handle image files using GalleryPage
                                                        return (

                                                            <GalleryPage
                                                                photos={[{
                                                                    src: `${STATIC_URL}/${upload}`,
                                                                    width: 4,
                                                                    height: 3
                                                                }]}
                                                                key={upload} // Use upload as key for uniqueness
                                                            />
                                                        );
                                                    } else if (fileExtension === 'pdf') {
                                                        // Handle PDF files
                                                        return (
                                                            <div className="col-md-3" key={upload}>
                                                                <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                    View PDF
                                                                </a>
                                                            </div>
                                                        );
                                                    } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                        // Handle Excel files
                                                        return (
                                                            <div className="col-md-3" key={upload}>
                                                                <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                    Download Excel File
                                                                </a>
                                                            </div>
                                                        );
                                                    } else {
                                                        // Handle other file types
                                                        return (
                                                            <div className="col-md-3" key={upload}>
                                                                <p>Unsupported file type: {fileExtension}</p>
                                                            </div>
                                                        );
                                                    }
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                );

            } else if (item.actionType === "ins_retake_actions") {

                cm = cm + 0.1

                return (

                    <div className="obs-section p-4">
                        <div className='row'>
                            <div className='col-6'>
                                <p className="obs-title"> Action Verifier Comments & Reassigned Action  - IA {cm} </p>
                                <p className="obs-content">{item.actionToBeTaken}</p>

                                <p className="obs-title"> Comments </p>
                                <p className="obs-content">{item.comments}</p>
                                {item.status !== 'open' && <>
                                    <p className="obs-title"> Action Taken </p>
                                    <p className="obs-content">{item.actionTaken}</p>
                                </>}
                                {item.status === 'open' ?
                                    <p className="obs-title"> Action Assignee</p>
                                    : <p className="obs-title"> Action Taken By</p>}
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            <div className='col-6'>
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>

                                <div className='col-6'>
                                    <p className="obs-title">Due Date</p>
                                    <p className="obs-content">{moment(item.dueDate).format('Do MMM YYYY')}</p>
                                </div>
                            </div>


                        </div>
                        <div className='row'>
                            <div className="col-md-12">
                                {item.uploads && item.uploads.length > 0 && (
                                    <div className='d-flex'>
                                        <div className='col-12'>
                                            <p className="obs-title">Images</p>
                                            {item.uploads.map((upload) => {
                                                const fileExtension = upload.split('.').pop().toLowerCase();

                                                if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                    // Handle image files using GalleryPage
                                                    return (
                                                        <GalleryPage
                                                            photos={[{
                                                                src: `${STATIC_URL}/${upload}`,
                                                                width: 4,
                                                                height: 3
                                                            }]}
                                                            key={upload} // Use upload as key for uniqueness
                                                        />
                                                    );
                                                } else if (fileExtension === 'pdf') {
                                                    // Handle PDF files
                                                    return (
                                                        <div className="col-md-3" key={upload}>
                                                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                View PDF
                                                            </a>
                                                        </div>
                                                    );
                                                } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                    // Handle Excel files
                                                    return (
                                                        <div className="col-md-3" key={upload}>
                                                            <a href={`${STATIC_URL}/${upload}`} target="_blank" rel="noopener noreferrer">
                                                                Download Excel File
                                                            </a>
                                                        </div>
                                                    );
                                                } else {
                                                    // Handle other file types
                                                    return (
                                                        <div className="col-md-3" key={upload}>
                                                            <p>Unsupported file type: {fileExtension}</p>
                                                        </div>
                                                    );
                                                }
                                            })}
                                        </div>
                                    </div>
                                )}

                            </div>
                        </div>
                    </div>
                );
            } else if (item.actionType === "ins_verify_actions") {


                return (
                    <div className="obs-section p-4">


                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Action Verifier</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            {item.actionType === 'submitted' &&
                                <div className="col-md-6">
                                    <p className="obs-title">Date</p>
                                    <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                                </div>
                            }
                        </div>
                        {item.actionType === 'submitted' &&
                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <p className="obs-title">Action Verifier Comments</p>
                                    <p className="obs-content">{item.comments}</p>
                                </div>
                            </div>
                        }



                    </div>
                );
            } else if (item.actionType === "approve" && item.status === 'submitted') {


                return (
                    <div className="obs-section p-4">


                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Action Verified By</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                            </div>
                        </div>
                        <div className="row mb-3">
                            <div className="col-md-6">
                                <p className="obs-title">Action Verifier Comments</p>
                                <p className="obs-content">{item.comments}</p>
                            </div>
                        </div>



                    </div>
                );
            } else if (item.actionType === 'reject' && item.status === 'submitted') {

                return (
                    <div className="obs-section p-4">
                        <div className='row'>

                            <div className="col-md-6">
                                <p className="obs-title">Action Verified By</p>
                                <p className="obs-content">{item.assignedToId &&
                                    getName(item.assignedToId
                                    )}</p>
                            </div>
                            <div className="col-md-6">
                                <p className="obs-title">Date</p>
                                <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
                            </div>
                        </div>
                    </div>
                )
            }
            return null; // Handle other cases if necessary
        });


    };

    // const getRAData = (data) => {
    //     doc = doc + 1.0
    //     return data.data.map((item, index) => {
    //         if (item.actionType === "take_actions_ra") {

    //             return (
    //                 <div className="obs-section p-4">
    //                     <div key={index} className="row ">
    //                         <div className='col-8'>
    //                             <p className="obs-title"> Assigned Action - DOC {doc.toFixed(1)} </p>
    //                             <p className="obs-content">{item.actionToBeTaken}</p>
    //                         </div>


    //                         {item.status === 'open' && <>
    //                             <p className="obs-title"> Action Assignee</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </>}
    //                         {item.status === 'submitted' && <>
    //                             <div className="row mb-3">
    //                                 <div className="col-md-12">
    //                                     <p className="obs-title">Action Taken </p>
    //                                     <p className="obs-content">{item.actionTaken}</p>
    //                                 </div>
    //                             </div>

    //                             <div className="row mb-3">
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Action Taken By</p>
    //                                     <p className="obs-content">{item.assignedToId &&
    //                                         getName(item.assignedToId
    //                                         )}</p>
    //                                 </div>
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Date</p>
    //                                     <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                                 </div>
    //                             </div>

    //                         </>
    //                         }
    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (<>
    //                                 <div className='d-flex'>
    //                                     <p className="obs-title"> Images</p>
    //                                     {item.uploads.map(item => (<>
    //                                         <GalleryPage photos={[{
    //                                             src: `${STATIC_URL}/${item}`,
    //                                             width: 4,
    //                                             height: 3
    //                                         }]} />

    //                                     </>

    //                                     ))}
    //                                 </div>

    //                             </>)}
    //                         </div>
    //                     </div>
    //                 </div>

    //             );

    //         } else if (item.actionType === "retake_actions") {

    //             doc = doc + 0.1

    //             return (

    //                 <div className="obs-section p-4">

    //                     <div className='row'>
    //                         <p className="obs-title"> Action Verifier Comments & Reassigned Action  - DOC {doc} </p>
    //                         <p className="obs-content">{item.comments}</p>
    //                         {item.status === 'open' ?
    //                             <p className="obs-title"> Action Assignee</p>
    //                             : <p className="obs-title"> Action Taken By</p>}
    //                         <p className="obs-content">{item.assignedToId &&
    //                             getName(item.assignedToId
    //                             )}</p>

    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (<>
    //                                 <div className='d-flex'>
    //                                     <p className="obs-title"> Images</p>
    //                                     {item.uploads.map(item => (<>
    //                                         <GalleryPage photos={[{
    //                                             src: `${STATIC_URL}/${item}`,
    //                                             width: 4,
    //                                             height: 3
    //                                         }]} />

    //                                     </>

    //                                     ))}
    //                                 </div>

    //                             </>)}
    //                         </div>
    //                     </div>

    //                 </div>
    //             );
    //         } else if (item.actionType === "approve" && item.status === 'submitted') {



    //             return (
    //                 <div className="obs-section p-4">


    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verified By</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </div>
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Date</p>
    //                             <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                         </div>
    //                     </div>
    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verifier Comments</p>
    //                             <p className="obs-content">{item.comments}</p>
    //                         </div>
    //                     </div>



    //                 </div>
    //             );
    //         } else if (item.actionType === 'reject' && item.status === 'submitted') {

    //             return (
    //                 <div className="obs-section p-4">


    //                     <div className="col-md-6">
    //                         <p className="obs-title">Action Verified By</p>
    //                         <p className="obs-content">{item.assignedToId &&
    //                             getName(item.assignedToId
    //                             )}</p>
    //                     </div>
    //                 </div>
    //             )
    //         }
    //         return null; // Handle other cases if necessary
    //     });
    // };
    // const getPicmData = (data) => {
    //     picm = picm + 1.0
    //     return data.data.map((item, index) => {
    //         if (item.actionType === "take_actions_control_post") {

    //             return (
    //                 <div className="obs-section p-4">
    //                     <div key={index} className="row ">
    //                         <div className='col-8'>
    //                             <p className="obs-title"> Assigned Action - PICM {picm.toFixed(1)} </p>
    //                             <p className="obs-content">{item.actionToBeTaken}</p>
    //                         </div>


    //                         {item.status === 'open' && <>
    //                             <p className="obs-title"> Action Assignee</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </>}
    //                         {item.status === 'submitted' && <>
    //                             <div className="row mb-3">
    //                                 <div className="col-md-12">
    //                                     <p className="obs-title">Action Taken </p>
    //                                     <p className="obs-content">{item.actionTaken}</p>
    //                                 </div>
    //                             </div>

    //                             <div className="row mb-3">
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Action Taken By</p>
    //                                     <p className="obs-content">{item.assignedToId &&
    //                                         getName(item.assignedToId
    //                                         )}</p>
    //                                 </div>
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Date</p>
    //                                     <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                                 </div>
    //                             </div>

    //                         </>
    //                         }
    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (<>
    //                                 <div className='d-flex'>
    //                                     <p className="obs-title"> Images</p>
    //                                     {item.uploads.map(item => (<>
    //                                         <GalleryPage photos={[{
    //                                             src: `${STATIC_URL}/${item}`,
    //                                             width: 4,
    //                                             height: 3
    //                                         }]} />

    //                                     </>

    //                                     ))}
    //                                 </div>

    //                             </>)}
    //                         </div>
    //                     </div>
    //                 </div>

    //             );

    //         } else if (item.actionType === "retake_actions") {

    //             picm = picm + 0.1

    //             return (

    //                 <div className="obs-section p-4">

    //                     <div className='row'>
    //                         <p className="obs-title"> Action Verifier Comments & Reassigned Action  - PICM {picm} </p>
    //                         <p className="obs-content">{item.comments}</p>
    //                         {item.status === 'open' ?
    //                             <p className="obs-title"> Action Assignee</p>
    //                             : <p className="obs-title"> Action Taken By</p>}
    //                         <p className="obs-content">{item.assignedToId &&
    //                             getName(item.assignedToId
    //                             )}</p>

    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (<>
    //                                 <div className='d-flex'>
    //                                     <p className="obs-title"> Images</p>
    //                                     {item.uploads.map(item => (<>
    //                                         <GalleryPage photos={[{
    //                                             src: `${STATIC_URL}/${item}`,
    //                                             width: 4,
    //                                             height: 3
    //                                         }]} />

    //                                     </>

    //                                     ))}
    //                                 </div>

    //                             </>)}
    //                         </div>
    //                     </div>

    //                 </div>
    //             );
    //         } else if (item.actionType === "approve" && item.status === 'submitted') {



    //             return (
    //                 <div className="obs-section p-4">


    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verified By</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </div>
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Date</p>
    //                             <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                         </div>
    //                     </div>
    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verifier Comments</p>
    //                             <p className="obs-content">{item.comments}</p>
    //                         </div>
    //                     </div>



    //                 </div>
    //             );
    //         } else if (item.actionType === 'reject' && item.status === 'submitted') {

    //             return (
    //                 <div className="obs-section p-4">


    //                     <div className="col-md-6">
    //                         <p className="obs-title">Action Verified By</p>
    //                         <p className="obs-content">{item.assignedToId &&
    //                             getName(item.assignedToId
    //                             )}</p>
    //                     </div>
    //                 </div>
    //             )
    //         }
    //         return null; // Handle other cases if necessary
    //     });
    // };
    const isJSON = (str) => {
        try {
            JSON.parse(str);
        } catch (e) {
            return false;
        }
        return true;
    };
    // const getNcData = (data) => {
    //     nc = nc + 1.0
    //     return data.data.map((item, index) => {
    //         if (item.actionType === "audit_take_actions") {

    //             return (
    //                 <div className="obs-section p-4">
    //                     <div key={index} className="row ">
    //                         <div className="row mb-4 p-2" style={{ border: '1px solid #e7e6e6' }}>
    //                             <div className="col-6">
    //                                 <p>Project/DC: <b>{current.locationFour.name}</b></p>
    //                                 <p>Start Date: <b>{moment(current.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>

    //                             </div>
    //                             <div className="col-6">
    //                                 <p>Auditor Name: <b>{current.assignedTo?.firstName}</b></p>
    //                                 <p>End Date: <b>{moment(current.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}</b></p>
    //                             </div>
    //                             <div className="col-12">
    //                                 <p>Findings: <b>{item.applicationDetails?.findings}</b></p>
    //                             </div>

    //                             <div className="col-6">
    //                                 <p>Category: <b>{item.applicationDetails?.category}</b></p>
    //                                 <p>Standards And References: <b>{item.applicationDetails?.standardsAndReferences}</b></p>
    //                                 <p>Recommended Mitigation Measures: <b>{item.applicationDetails?.recommendations}</b></p>
    //                             </div>
    //                             <div className="col-6">
    //                                 <p>Classification: <b>{item.applicationDetails?.classification}</b></p>
    //                                 <p>Potential Consequences: <b>{item.applicationDetails?.potentialHazard}</b></p>
    //                                 <p>Due Date: <b>{item.dueDate}</b></p>
    //                             </div>

    //                         </div>
    //                         <div className='col-8'>
    //                             <p className="obs-title"> Assigned Action - NC {nc.toFixed(1)} </p>
    //                             <p className="obs-content">{item.actionToBeTaken}</p>
    //                         </div>


    //                         {item.status === 'open' && <>
    //                             <p className="obs-title"> Action Assignee</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </>}
    //                         {item.status === 'submitted' && <>
    //                             <div className="row mb-3">
    //                                 {isJSON(item.actionTaken) ? <>

    //                                     <div className="col-md-6">

    //                                         <p className='obs-title'>Identify the Root Cause(s)</p>

    //                                         <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).rootCause }} />
    //                                     </div>
    //                                     <div className="col-md-6">

    //                                         <p className='obs-title'>Identified Corrective Actions</p>
    //                                         <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).correctiveAction }} />
    //                                         {/* <p className="obs-content">{JSON.parse(item.actionTaken).correctiveAction}</p> */}
    //                                     </div>
    //                                     <div className="col-md-6">

    //                                         <p className='obs-title'>Description of the Action Taken</p>
    //                                         <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).actionDesc }} />
    //                                         {/* <p className="obs-content">{JSON.parse(item.actionTaken).actionDesc}</p> */}
    //                                     </div>
    //                                 </>
    //                                     :
    //                                     <div className="col-md-12">
    //                                         <p className="obs-title">Action Taken </p>
    //                                         <p className="obs-content">{item.actionTaken}</p>


    //                                     </div>
    //                                 }

    //                             </div>

    //                             <div className="row mb-3">
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Action Taken By</p>
    //                                     <p className="obs-content">{item.assignedToId &&
    //                                         getName(item.assignedToId
    //                                         )}</p>
    //                                 </div>
    //                                 <div className="col-md-6">
    //                                     <p className="obs-title">Date</p>
    //                                     <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                                 </div>
    //                             </div>

    //                         </>
    //                         }
    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (
    //                                 <>
    //                                     <div className='d-flex'>
    //                                         <p className="obs-title">Files</p>
    //                                         {item.uploads.map(file => {
    //                                             const fileExtension = file.split('.').pop().toLowerCase();

    //                                             if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
    //                                                 // Handle image files
    //                                                 return (
    //                                                     <GalleryPage
    //                                                         photos={[{
    //                                                             src: `${STATIC_URL}/${file}`,
    //                                                             width: 4,
    //                                                             height: 3
    //                                                         }]}
    //                                                     />
    //                                                 );
    //                                             } else if (fileExtension === 'pdf') {
    //                                                 // Handle PDF files (as URLs)
    //                                                 return (
    //                                                     <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
    //                                                         View PDF
    //                                                     </a>
    //                                                 );
    //                                             } else if (['xls', 'xlsx'].includes(fileExtension)) {
    //                                                 // Handle Excel files
    //                                                 return (
    //                                                     <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
    //                                                         Download Excel File
    //                                                     </a>
    //                                                 );
    //                                             } else {
    //                                                 // Handle other file types or show a default message
    //                                                 return (
    //                                                     <p>Unsupported file type: {fileExtension}</p>
    //                                                 );
    //                                             }
    //                                         })}
    //                                     </div>
    //                                 </>
    //                             )}
    //                         </div>
    //                     </div>
    //                 </div>

    //             );

    //         } else if (item.actionType === "aud_retake_actions") {

    //             nc = nc + 0.1

    //             return (

    //                 <div className="obs-section p-4">

    //                     <div className='row'>
    //                         <p className="obs-title"> Action Verifier Comments & Reassigned Action  - NC {nc} </p>
    //                         <p className="obs-content">{item.comments}</p>


    //                         {item.status === 'open' ?
    //                             <p className="obs-title"> Action Assignee</p>
    //                             : <p className="obs-title"> Action Taken By</p>}
    //                         <p className="obs-content">{item.assignedToId &&
    //                             getName(item.assignedToId
    //                             )}</p>

    //                     </div>

    //                     <div className="row mb-3">
    //                         {isJSON(item.actionTaken) && <>

    //                             <div className="col-md-6">

    //                                 <p className='obs-title'>Identify the Root Cause(s)</p>

    //                                 <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).rootCause }}></p>
    //                             </div>
    //                             <div className="col-md-6">

    //                                 <p className='obs-title'>Identified Corrective Actions</p>

    //                                 <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).correctiveAction }}></p>
    //                             </div>
    //                             <div className="col-md-6">

    //                                 <p className='obs-title'>Description of the Action Taken</p>

    //                                 <p className="obs-content" dangerouslySetInnerHTML={{ __html: JSON.parse(item.actionTaken).actionDesc }}></p>
    //                             </div>
    //                         </>

    //                         }

    //                     </div>
    //                     <div className='row'>


    //                         <div className="col-md-12">

    //                             {item.uploads && item.uploads.length > 0 && (
    //                                 <>
    //                                     <div className='d-flex'>
    //                                         <p className="obs-title">Files</p>
    //                                         {item.uploads.map(file => {
    //                                             const fileExtension = file.split('.').pop().toLowerCase();

    //                                             if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
    //                                                 // Handle image files
    //                                                 return (
    //                                                     <GalleryPage
    //                                                         photos={[{
    //                                                             src: `${STATIC_URL}/${file}`,
    //                                                             width: 4,
    //                                                             height: 3
    //                                                         }]}
    //                                                     />
    //                                                 );
    //                                             } else if (fileExtension === 'pdf') {
    //                                                 // Handle PDF files (as URLs)
    //                                                 return (
    //                                                     <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
    //                                                         View PDF
    //                                                     </a>
    //                                                 );
    //                                             } else if (['xls', 'xlsx'].includes(fileExtension)) {
    //                                                 // Handle Excel files
    //                                                 return (
    //                                                     <a href={`${STATIC_URL}/${file}`} target="_blank" rel="noopener noreferrer">
    //                                                         Download Excel File
    //                                                     </a>
    //                                                 );
    //                                             } else {
    //                                                 // Handle other file types or show a default message
    //                                                 return (
    //                                                     <p>Unsupported file type: {fileExtension}</p>
    //                                                 );
    //                                             }
    //                                         })}
    //                                     </div>
    //                                 </>
    //                             )}
    //                         </div>
    //                     </div>

    //                 </div>
    //             );
    //         } else if (item.actionType === "approve" && item.status === 'submitted') {



    //             return (
    //                 <div className="obs-section p-4">


    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verified By</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </div>
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Date</p>
    //                             <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                         </div>
    //                     </div>
    //                     <div className="row mb-3">
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verifier Comments</p>
    //                             <p className="obs-content">{item.comments}</p>
    //                         </div>
    //                     </div>



    //                 </div>
    //             );
    //         } else if (item.actionType === 'reject' && item.status === 'submitted') {

    //             return (
    //                 <div className="obs-section p-4">

    //                     <div className='row'>
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Action Verified By</p>
    //                             <p className="obs-content">{item.assignedToId &&
    //                                 getName(item.assignedToId
    //                                 )}</p>
    //                         </div>
    //                         <div className="col-md-6">
    //                             <p className="obs-title">Date</p>
    //                             <p className="obs-content">{convertToLocalTime(item.createdDate)}</p>
    //                         </div>
    //                     </div>
    //                 </div>
    //             )
    //         }
    //         return null; // Handle other cases if necessary
    //     });
    // };

    const processedActions = useMemo(() => {
        let cmCounter = 0.0;
        return totalAction
            .map(action => {
                if (action.firstActionType === 'ins_take_actions_control') {
                    cmCounter += 1.0;
                    return { ...action, cm: cmCounter };
                }
                return null;
            })
            .filter(action => action !== null);
    }, [totalAction]);


    const handleFieldChange = (index, field, value) => {
        const updatedMeasures = [...controlMeasures];
        updatedMeasures[index][field] = value;
        setControlMeasures(updatedMeasures);
    };

    const handleAddControlMeasure = () => {
        // Calculate the next sequence number based on both existing processed actions
        // and any control measures already added but not yet saved
        const nextSequenceNo = processedActions.length + controlMeasures.length + 1;
        console.log(`Adding new control measure with sequenceNo: ${nextSequenceNo}`);

        setControlMeasures([
            ...controlMeasures,
            {
                actionToBeTaken: "",
                dueDate: "",
                actionOwner: "",
                sequenceNo: nextSequenceNo
            },
        ]);
        setValidationControlErrors([
            ...validationControlErrors,
            { controlMeasures: false, completionDate: false, personResponsible: false },
        ]);
    };

    // Handle Delete Control Measure
    const handleDeleteControlMeasure = (index) => {
        const updatedMeasures = controlMeasures.filter((_, i) => i !== index);
        const updatedErrors = validationControlErrors.filter((_, i) => i !== index);
        setControlMeasures(updatedMeasures);
        setValidationControlErrors(updatedErrors);
    };

    const validateFieldsControl = () => {
        const errors = controlMeasures.map((item) => ({
            controlMeasures: !item.controlMeasures.trim(),
            completionDate: !item.completionDate.trim(),
            personResponsible: !item.personResponsible,
        }));
        setValidationControlErrors(errors);
        return errors.every((error) => !Object.values(error).includes(true));
    };

    const toggleModal = () => {
        setShowModal(!showModal);
        setValidationControlErrors([]); // Reset validation errors when closing modal
    };

    const handleSave = async () => {
        try {
            const updatedControlMeasures = [...controlMeasures];

            // Ensure each control measure has a sequence number
            for (let i = 0; i < updatedControlMeasures.length; i++) {
                // If sequenceNo is not set, use the index + existing processed actions count + 1
                if (!updatedControlMeasures[i].sequenceNo) {
                    updatedControlMeasures[i].sequenceNo = processedActions.length + i + 1;
                }

                // Handle file uploads
                const files = controlMeasureFiles[i];
                if (files && files.length > 0) {
                    const formData = new FormData();
                    files.forEach(file => formData.append('file', file));

                    const token = localStorage.getItem('access_token');
                    const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authorization': `Bearer ${token}`,
                        },
                    });

                    if (fileResponse.status === 200) {
                        const uploadedFileNames = fileResponse.data.files.map(f => f.originalname);
                        updatedControlMeasures[i].uploads = uploadedFileNames;
                    }
                }
            }

            const payload = { postActions: updatedControlMeasures };
            console.log('Saving with payload:', payload);

            const response = await API.patch(INSPECTION_CONTROL_MEASURE_ID(current.id), payload);

            if (response.status === 204) {
                Swal.fire({
                    title: "Success!",
                    text: "Data updated successfully",
                    icon: "success",
                    confirmButtonText: "Ok",
                }).then(() => window.location.reload());
            } else {
                console.error("Unexpected response status:", response.status);
            }
        } catch (error) {
            console.error("Error saving control measures:", error);
            Swal.fire({
                title: "Error!",
                text: "Something went wrong. Please try again.",
                icon: "error",
                confirmButtonText: "OK",
            });
        }
    };

    const matchedResults = [];
    let label = '';
    let i = null;
    let index = null;

    processedActions.forEach(action => {
        const actionToBeTaken = action.data?.[0]?.actionToBeTaken;

        if (actionToBeTaken) {
            const postMatch = current.postActions?.find(
                p => p.actionToBeTaken === actionToBeTaken
            );

            if (postMatch) {
                // Find matching label in checklist
                let label = '';
                let main = ''
                current.checklist.value.forEach((group, groupIndex) => {
                    if (postMatch.index === groupIndex) {
                        if (group.type === "checklist-group" && Array.isArray(group.questions)) {

                            group.questions.forEach((question, questionIndex) => {
                                if (postMatch.i === questionIndex) {
                                    label = question.label;
                                    i = groupIndex;
                                    index = questionIndex;
                                    main = group.label
                                }
                            });
                        }
                    }

                });

                matchedResults.push({
                    actionToBeTaken,
                    i: postMatch.i,
                    index: postMatch.index,
                    label,
                    main
                });
            }
        }
    });

    console.log(matchedResults);
    const getFileAddedMessage = (fileName) => {
        return `${fileName} successfully added.`;
    };

    const getPreviewIcon = (fileObject, classes) => {
        const { type, name } = fileObject.file;
        if (type === 'application/pdf') {
            return <PdfIcon className={classes.image} />;
        } else if (name.endsWith('.xlsx') || name.endsWith('.xls')) {
            return <DescriptionIcon className={classes.image} />;
        }
        // Default preview for non-PDF/Excel files
        return (
            <img
                alt={fileObject.file.name}
                className={classes.image}
                src={fileObject.data}
            />
        );
    };

    return (<>
        <h4 className='mb-3 fw-bold'>{id} Actions</h4>
        <>
            {(validationRoles.some(role => role.name === 'Country EHS Director')) && (
                <div className="d-flex justify-content-end mb-3">
                    <Button onClick={() => setShowModal(true)} className="me-3">Add Inspection Actions</Button>

                </div>
            )}

        </>
        {console.log(processedActions)}



        <Accordion>
            {processedActions.map((action, index) => {
                console.log(matchedResults);

                const matched = matchedResults[index];

                return (
                    action.firstActionType === 'ins_take_actions_control' && (
                        <AccordionTab
                            key={index}
                            header={getStatusAction(action, 'cm')}
                        >
                            <h4 className="mb-3" dangerouslySetInnerHTML={{ __html: matched?.main || '' }} />
                            <h4 dangerouslySetInnerHTML={{ __html: matched?.label || '' }} />

                            {getCMData(action, action.cm, index)}
                        </AccordionTab>
                    )
                );
            })}
        </Accordion>



        <Modal show={showModal} onHide={toggleModal} size="lg">
            <Modal.Header closeButton>
                <Modal.Title>Inspection Actions</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {controlMeasures.length > 0 ? (
                    controlMeasures.map((action, index) => (
                        <div className="border rounded p-3 mb-4" key={index}>
                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <label className="form-label fw-bold">
                                        Action IA-{action.sequenceNo || (processedActions.length + index + 1)}
                                    </label>
                                    <input
                                        className={`form-control ${validationControlErrors[index]?.controlMeasures ? "is-invalid" : ""}`}
                                        type="text"
                                        value={action.actionToBeTaken}
                                        onChange={(e) => handleFieldChange(index, "actionToBeTaken", e.target.value)}
                                        placeholder="Enter action to be taken"
                                    />
                                    {validationControlErrors[index]?.actionToBeTaken && (
                                        <div className="invalid-feedback">This field is required.</div>
                                    )}
                                </div>

                                <div className="col-md-6">
                                    <label className="form-label fw-bold">Due Date</label>
                                    {readOnly ? (
                                        <input
                                            className="form-control"
                                            type="text"
                                            value={action.dueDate ? moment(action.dueDate, "YYYY-MM-DD").format("Do MMM YYYY") : ""}
                                            disabled
                                        />
                                    ) : (
                                        <DatePicker
                                            selected={action.dueDate ? new Date(action.dueDate) : null}
                                            onChange={(date) => {
                                                if (date) {
                                                    // Set time to noon to avoid timezone issues
                                                    const adjustedDate = new Date(date);
                                                    adjustedDate.setHours(12, 0, 0, 0);
                                                    handleFieldChange(index, "dueDate", adjustedDate.toISOString());
                                                } else {
                                                    handleFieldChange(index, "dueDate", "");
                                                }
                                            }}
                                            dateFormat="dd-MM-yyyy"
                                            className={`form-control ${validationControlErrors[index]?.dueDate ? "is-invalid" : ""}`}
                                            placeholderText="Select due date"
                                            minDate={new Date()}
                                        />
                                    )}
                                    {validationControlErrors[index]?.dueDate && (
                                        <div className="invalid-feedback">Due Date is required.</div>
                                    )}
                                </div>
                            </div>

                            <div className="row mb-3">
                                <div className="col-md-6">
                                    <label className="form-label fw-bold">Person Responsible</label>
                                    <Select
                                        value={locationUsers.find(option => option.value === action.actionOwner)}
                                        isDisabled={readOnly}
                                        options={locationUsers.sort((a, b) => a.label.localeCompare(b.label))}
                                        onChange={(selectedOption) => handleFieldChange(index, "actionOwner", selectedOption?.value)}
                                        placeholder="Choose"
                                        className={validationControlErrors[index]?.actionOwner ? "is-invalid" : ""}
                                    />
                                    {validationControlErrors[index]?.actionOwner && (
                                        <div className="invalid-feedback d-block">Person Responsible is required.</div>
                                    )}
                                </div>
                                <div className="col-md-6">
                                    <label className="form-label fw-bold">Upload Evidence</label>
                                    <DropzoneArea
                                        acceptedFiles={[
                                            'image/jpeg',
                                            'image/png',
                                            'application/pdf',
                                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                            'application/vnd.ms-excel'
                                        ]}
                                        dropzoneText={"Drag and Drop / Upload Evidence"}
                                        filesLimit={5}
                                        maxFileSize={104857600}
                                        onChange={(files) => handleFileChange(index, files)}
                                        showPreviewsInDropzone={true}
                                        getFileAddedMessage={getFileAddedMessage}
                                        getPreviewIcon={getPreviewIcon}
                                    />
                                </div>
                            </div>

                            {!readOnly && (
                                <div className="text-end">
                                    <Button variant="danger" onClick={() => handleDeleteControlMeasure(index)} className="btn-sm">
                                        <i className="pi pi-trash" /> Remove Action
                                    </Button>
                                </div>
                            )}
                        </div>
                    ))
                ) : (
                    <div className="text-center">
                        {!readOnly && (
                            <Button variant="success" onClick={handleAddControlMeasure}>
                                Add Inspection Action
                            </Button>
                        )}
                    </div>
                )}

                {!readOnly && controlMeasures.length > 0 && (
                    <div className="text-center mt-3">
                        <Button variant="success" onClick={handleAddControlMeasure}>
                            Add Another Action
                        </Button>
                    </div>
                )}

            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={toggleModal}>
                    Close
                </Button>
                {!readOnly && (
                    <Button variant="primary" onClick={() => handleSave('control')}>
                        Save Changes
                    </Button>
                )}
            </Modal.Footer>
        </Modal>
    </>
    );
};

export default ActionTable;
