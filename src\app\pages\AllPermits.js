import React, { useState, useEffect, useRef } from "react";
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { eptwColumns, tableOptions } from './TableColumns';
import PermitModal from './PermitModal';
import { ALL_PERMITS_URL, PERMIT_REPORTS, PERMIT_REPORT_WITH_ID, STATIC_URL } from "../constants";
import API from "../services/API";
import AllFilterLocation from "./AllLocationFilter";
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import moment from 'moment'
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import { But<PERSON> } from 'primereact/button';
import * as XLSX from 'xlsx';
import $ from "jquery";
import DcOpsForm from "./Eptw/DcOpsForm";
import Construction from "./Eptw/Construction";
import { useSelector } from "react-redux";
window.jQuery = $;
// @ts-ignore
window.$ = $;
export const AllPermits = ({ data, from }) => {
    console.log(data)
    const dataTableRef = useRef();
    const [permits, setPermits] = useState([]);
    const [filterData, setFilterData] = useState([]);
    const [applicant, setApplicant] = useState([]);
    const [filters, setFilters] = useState(null);
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [status, setStatus] = useState([])
    const [closure, setClosure] = useState([])
    const [type, setType] = useState([])
    const [dcshow, setDcshow] = useState(false)
    const [conshow, setConshow] = useState(false)
    const me = useSelector((state) => state.login.user);
    const hasPTWApplicantRole = me?.validationRoles?.some((item) => item.name === 'PTW Applicant');

    console.log(hasPTWApplicantRole); // This will log true if the role exists, false otherwise.



    // useEffect(() => {
    //     getPermits();
    // }, [])
    // const getPermits = async () => {
    //     const response = await API.get(ALL_PERMITS_URL);
    //     if (response.status === 200) {
    //         setPermits(response.data)
    //         setFilterData(response.data)
    //     }
    // }

    useEffect(() => {
        if (data) {
            getPermitData();

        }
        setPermits(data)
        setFilterData(data)
        initFilters();

    }, [data])

    const getPermitData = () => {

        const obs = data.map(item => {
            return { name: item.applicant.firstName || '', value: item.applicant.firstName || '' }
        })
        setApplicant(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const status = data.map(item => {
            return {
                name: item.status === "Pending DCSO Isolation" ? "Pending DCSO Action" : item.status,
                value: item.status
            };
        });

        setStatus(
            status.filter((ele, ind) =>
                ind === status.findIndex(elem => elem.value === ele.value && elem.name === ele.name)
            )
        );

        const clo = data.map(item => {
            return { name: item.closure ? (item.closure.status ? item.closure.status : 'N/A') : 'N/A', value: item.closure ? (item.closure.status ? item.closure.status : 'N/A') : 'N/A' }
        })
        setClosure(clo.filter((ele, ind) => ind === clo.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

        const type = data.map(item => {
            return { name: item.permitType, value: item.permitType }
        })
        setType(type.filter((ele, ind) => ind === type.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
    }

    const initFilters = () => {
        setFilters({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            maskId: { value: null, matchMode: FilterMatchMode.IN },
            "applicant.firstName": { value: null, matchMode: FilterMatchMode.IN },
            "closure.status": { value: null, matchMode: FilterMatchMode.IN },
            status: { value: null, matchMode: FilterMatchMode.IN },
            permitType: { value: null, matchMode: FilterMatchMode.IN },
        });
        setGlobalFilterValue('');
    };
    const columns121 = [
        { dataField: 'maskId', text: 'ID' },
        { dataField: 'created', text: 'Submitted On' },
        { dataField: 'permitType', text: 'Type Of Permit' },
        { dataField: 'location', text: 'Location' },
        { dataField: 'closeoutStatus', text: 'Closure Status' },
        { dataField: 'closeoutDate', text: 'Closeout Date' },
        { dataField: 'firstName', text: 'Submitted By' },
        { dataField: 'status', text: 'Status' },

    ];
    const exportCSV = () => {

        const data = filterData.map(item => {

            item.closeoutDate = item.closure ?
                moment(item.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A') :
                'N/A';

            item.firstName = item.applicant.firstName;

            item.closeoutStatus = item.closure ?
                item.closure.status :
                'N/A';

            item.location = `${item.locationOne?.name || ''} > ${item.locationTwo?.name || ''} > ${item.locationThree?.name || ''} > ${item.locationFour?.name || ''} > ${item.locationFive?.name || ''} > ${item.locationSix?.name || ''}`

            return item;
        });
        const modifiedData1 = data.map(item => {
            const modifiedItem = {};
            columns121.forEach(column => {
                modifiedItem[column.text] = item[column.dataField];
            });
            return modifiedItem;
        });
        const ws = XLSX.utils.json_to_sheet(modifiedData1);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        XLSX.writeFile(wb, `${'All Permit' + moment().format('DD-MM-YYYY HH:MM')}.xlsx`);

    }
    const renderHeader = () => {
        if (!from) {

            return (
                <div className='d-flex justify-content-end align-items-end'>
                    {/* {hasPTWApplicantRole && <>
                        <Button label="Apply Construction Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setConshow(true)} />
                        <Button label="Apply DC Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setDcshow(true)} />
                    </>} */}

                    <Button label="Export" icon="pi pi-upload" className="p-button" onClick={exportCSV} />

                </div>
            );
        }
    };

    const header = renderHeader();

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        const filteredData = permits.filter(item => {
            return (
                (locationOneId === '' || item.locationOneId === locationOneId) &&
                (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
                (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
                (locationFourId === '' || item.locationFourId === locationFourId)
            );
        });

        setFilterData(filteredData);
    };

    const [showReportModal, setShowReportModal] = useState(false);
    const [reportData, setReportData] = useState(null);
    const viewObservationReport = async (id) => {

        const params = {
            "include": [{ "relation": "permitReportAction" }, { "relation": "applicant" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

        };
        const response = await API.get(`${PERMIT_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

        if (response.status === 200) {

            const actionUploads = (response.data.permitReportActions && response.data.permitReportActions.length) ? response.data.permitReportActions.flatMap(obj => obj.uploads) : [];

            response.data.uploads = [...response.data.uploads]

            response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []


            setReportData(response.data)
            setShowReportModal(true)
        }

    }
    const defaultMaterialTheme = createTheme();

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'visibility',
            tooltip: 'View Report',
            onClick: (event, rowData) => {
                // Do save operation
                console.log(rowData)
                viewObservationReport(rowData.id)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'View'
        }
    };

    const maskIdBodyTemplate = (row) => {

        return (
            <div className='maskid' onClick={() => viewObservationReport(row.id)}>
                {row.maskId}
            </div>
        );

    }

    const locationBodyTemplate = (rowData) => {

        if (rowData.zonesAndLevels) {
            return `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''} > ${rowData.zonesAndLevels[0].locationFive.name || ''} > ${rowData.zonesAndLevels[0].locationSix.name || ''}`

        } else {
            return `${rowData.locationOne?.name || ''} > ${rowData.locationTwo?.name || ''} > ${rowData.locationThree?.name || ''} > ${rowData.locationFour?.name || ''} > ${rowData.locationFive?.name || ''} > ${rowData.locationSix?.name || ''}`
        }


    }
    const statusBodyTemplate = (rowData) => {
        return rowData.closure ? (rowData.closure.status ? rowData.closure.status : 'N/A') : 'N/A'
    }
    const dateBodyTemplate = (rowData) => {
        return rowData.closure && rowData.closure.closeoutDate
            ? moment(rowData.closure.closeoutDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A')
            : 'N/A';
    }

    const createdBodyTemplate = (rowData) => {
        return moment(rowData.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY')

    }

    const submitFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={applicant} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const statusFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={status} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const closureFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={closure} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const typeFilterTemplate = (options) => {

        return (
            <React.Fragment>
                <MultiSelect value={options.value} options={type} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    }
    const sortDate = (e) => {
        console.log(e)
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                // Parse the dates using Moment.js
                const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);
                const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            });
        } else {

            return e.data.sort((a, b) => {
                // Parse the dates using Moment.js
                const dateA = moment(a.created, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY']);
                const dateB = moment(b.created, ['DD-MM-YYYY HH:mm', 'DD-MM-YYYY hh:mm A', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'Do MMM YYYY hh:mm A', 'Do MMM YYYY']);

                // Compare the dates
                if (dateA.isBefore(dateB)) {
                    return -1; // dateA comes before dateB
                } else if (dateA.isAfter(dateB)) {
                    return 1; // dateA comes after dateB
                } else {
                    return 0; // dates are equal
                }
            }).reverse()
        }
    }
    const permitTypeBodyTemplate = (row) => {
        if (row.permitType === 'CA') {
            return (
                <>Construction</>
            )
        } else {
            return row.permitType
        }

    }

    const statusDisplayName = (status) => {
        return status === "Pending DCSO Isolation" ? "Pending DCSO Action" : status;
    };
    return (
        <>

            {/* <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} /> */}
            {/* <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={eptwColumns}
                    data={filterData}
                    title=""
                    style={tableStyle}
                    actions={tableActions}
                    options={{ pageSize: 20 }}
                    localization={localization}
                />
            </ThemeProvider> */}
            <DataTable value={filterData} paginator rows={10} ref={dataTableRef} header={header} filters={filters} globalFilterFields={["maskId"]} onFilter={(e) => { setFilters(e.filters) }} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found." >

                {/* <Column field='color' body={statusBodyTemplate} filter filterElement={statusFilterTemplate} showFilterMatchModes={false}></Column> */}

                <Column field='maskId' body={maskIdBodyTemplate} header="ID" headerStyle={{ width: '15%' }} sortable></Column>

                <Column field='created' sortFunction={sortDate} sortable header="Submitted On" headerStyle={{ width: '10%' }} ></Column>

                <Column field='permitType' header="Type of Permit" headerStyle={{ width: '10%' }} filterElement={typeFilterTemplate} filter showFilterMatchModes={false}></Column>

                <Column field="location" header="Location" body={locationBodyTemplate} ></Column>
                <Column field="dcop.ticketNo" header="Change Ticket No"  ></Column>

                <Column field="closure.status" header="Closure Status" body={statusBodyTemplate} filterElement={closureFilterTemplate} filter showFilterMatchModes={false}></Column>

                <Column field="closure" header="Closeout Date" body={dateBodyTemplate}></Column>


                <Column field="applicant.firstName" header="Applied By" filterElement={submitFilterTemplate} filter showFilterMatchModes={false}></Column>

                <Column field="status" header="Status" body={(rowData) => statusDisplayName(rowData.status)} filterElement={statusFilterTemplate} filter showFilterMatchModes={false} ></Column>



            </DataTable>
            {showReportModal &&

                <PermitModal reportData={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />

            }

            {dcshow &&
                <DcOpsForm show={dcshow} handleClose={() => setDcshow(false)} />
            }
            {conshow &&
                <Construction show={conshow} handleClose={() => setConshow(false)} />
            }
        </>
    )
}

export default AllPermits;