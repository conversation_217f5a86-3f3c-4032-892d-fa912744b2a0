import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import moment from "moment";
import { ALL_INCIDENT_URL } from "../constants";
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';

const TopCategoriesBarChart = ({ dateRange, filterCriteria }) => {
    const impactLevels = [
  'Near-Miss',
  'Level 1 (First Aid Incident - FAI)',
  'Level 2 (Medical Treatment Incident - MTI)',
  'Level 3 (Lost Time Incident - LTI)',
  'Level 4 (High Severity Incident)',
  'Level 5 (Critical Incident)'
];
    const [chartData, setChartData] = useState(null);
    const [selectedImpacts, setSelectedImpacts] = useState(impactLevels);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const params = {
                    include: [
                        { relation: "locationOne" },
                        { relation: "locationThree" },
                    ],
                };

                const response = await API.get(
                    `${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`
                );

                let incidents = response.data;




                incidents = incidents.filter(item => {
                    // Extract country from locationOne.name (text inside parentheses)
                    const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                    // Determine BU from locationThree.name
                    let bu = "Other";
                    const buName = item.locationThree?.name || "";

                    if (/Construction|Fitouts/i.test(buName)) {
                        bu = "Construction";
                    } else if (/DC|Data Center|Data Centre/i.test(buName)) {
                        bu = "DC";
                    } else if (/Office/i.test(buName)) {
                        bu = "Office";
                    }

                    // Normalize impact level
                    const impactRaw = (item.actualImpact || '').toLowerCase().trim();
                    let impactLevel = null;
                    if (impactRaw.includes("near miss")) {
                        impactLevel = "Near-Miss";
                    } else if (impactRaw.includes("level 1")) {
                        impactLevel = "Level 1 (First Aid Incident - FAI)";
                    } else if (impactRaw.includes("level 2")) {
                        impactLevel = "Level 2 (Medical Treatment Incident - MTI)";
                    } else if (impactRaw.includes("level 3")) {
                        impactLevel = "Level 3 (Lost Time Incident - LTI)";
                    } else if (impactRaw.includes("level 4")) {
                        impactLevel = "Level 4 (High Severity Incident)";
                    } else if (impactRaw.includes("level 5")) {
                        impactLevel = "Level 5 (Critical Incident)";
                    }





                    // Now apply the filters
                    const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
                    const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
                    const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;
                    const isImpactSelected = selectedImpacts.length === 0 || 
                        (impactLevel && selectedImpacts.includes(impactLevel));

                    return isCountrySelected && isBUSelected && isSiteSelected && isImpactSelected;
                });

                const [startDate, endDate] = dateRange.map((date) => moment(date));

                // Step 1: Filter by incidentDate within dateRange
                const filteredIncidents = incidents.filter((incident) => {
                    const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A", true);
                    return (
                        incidentMoment.isValid() &&
                        incidentMoment.isBetween(startDate, endDate, null, "[]")
                    );
                });

                // Step 2: Group counts by month-year and category
                const categoryCountsByMonth = {};

                filteredIncidents.forEach((incident) => {
                    const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A");
                    const monthYear = incidentMoment.format("YYYY-MM");
                    const category = incident.IncidentCategory || "Unknown";

                    if (!categoryCountsByMonth[monthYear]) {
                        categoryCountsByMonth[monthYear] = {};
                    }

                    if (!categoryCountsByMonth[monthYear][category]) {
                        categoryCountsByMonth[monthYear][category] = 0;
                    }

                    categoryCountsByMonth[monthYear][category] += 1;
                });

                const allMonths = [];
                let current = startDate.clone().startOf("month");

                while (current.isSameOrBefore(endDate, "month")) {
                    allMonths.push(current.format("YYYY-MM"));
                    current.add(1, "month");
                }

                // Ensure all months exist in the data
                const formattedData = allMonths.map((monthYear) => {
                    const categoryCounts = categoryCountsByMonth[monthYear] || {};

                    const categoryEntries = Object.entries(categoryCounts)
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 5)
                        .map(([category, count]) => ({ category, count }));

                    return { monthYear, categories: categoryEntries };
                });

                const labels = allMonths.map((monthYear) =>
                    moment(monthYear, "YYYY-MM").format("MMM YYYY")
                );

                // Step 4: Find all unique categories across filtered months
                const allCategories = [
                    ...new Set(
                        formattedData.flatMap((item) =>
                            item.categories.map((cat) => cat.category)
                        )
                    ),
                ];

                // Step 5: Build series
                const seriesData = allCategories.map((category) => ({
                    name: category,
                    data: formattedData.map((monthData) => {
                        const cat = monthData.categories.find((c) => c.category === category);
                        return cat ? cat.count : 0;
                    }),
                }));

                setChartData({ labels, series: seriesData });
            } catch (error) {
                console.error("Error fetching top categories data:", error);
            }
        };

        fetchData();
    }, [dateRange, selectedImpacts]);

    const options = chartData
        ? {
            chart: {
                type: "column",
                zoomType: "xy",
            },
            title: {
                text: "",
            },
            xAxis: {
                categories: chartData.labels,
                title: {
                    text: "Month-Year",
                },
                crosshair: true,
            },
            yAxis: {
                min: 0,
                title: {
                    text: "Incident Count",
                },
            },
            tooltip: {
                shared: true,
                pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
            },
            plotOptions: {
                column: {
                    grouping: true,
                    dataLabels: {
                        enabled: true,
                    },
                },
            },
            legend: {
                enabled: true,
            },
            series: chartData.series,
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            "downloadPNG",
                            "downloadJPEG",
                            "downloadPDF",
                            "downloadSVG",
                            "separator",
                            "downloadCSV",
                            "downloadXLS",
                        ],
                    },
                },
            },
        }
        : null;

    const handleImpactChange = (value) => {
        setSelectedImpacts(value);
    };

    return (
        <div>
            <div style={{ marginBottom: '20px' }}>
                <MultiSelect
                    value={selectedImpacts}
                    options={impactLevels}
                    onChange={(e) => handleImpactChange(e.value)}
                    placeholder="Select Impact Levels"
                    style={{ width: '50%' }}
                    display="chip"
                />
            </div>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default TopCategoriesBarChart;
