import React from 'react';
import { STATIC_URL } from '../constants';

const FormRender = ({ formData }) => {
    const styles = {
        formContainer: {

        },
        formGroup: {
            marginBottom: '15px',
        },
        label: {
            fontWeight: 'bold',
            display: 'block',
            marginBottom: '5px',
        },
        textDisplay: {
            padding: '8px',
            margin: '5px 0',
            background: '#e9ecef',
            borderRadius: '4px',
        },
        imageList: {
            listStyleType: 'none',
            paddingLeft: '0',
        },
        imageItem: {
            borderRadius: '4px',
            margin: '5px 0',
            maxWidth: '100%', // Ensure images are responsive
        }
    };

    const renderImages = (imageUrls) => {
        if (imageUrls) {
            const urls = imageUrls.split(', ');
            return (
                <ul style={styles.imageList}>
                    {urls.map((url, index) => (
                        <li key={index}>
                            <img src={`${STATIC_URL}/${url}`} alt={`Uploaded content ${index + 1}`} style={styles.imageItem} />
                        </li>
                    ))}
                </ul>
            );
        }

    };

    return (
        <div style={styles.formContainer}>
            {formData.map((item, index) => {

                if (item.type === 'checklist-group') {
                    const isEnabled = item.enabled === true;
                    const label = item.label || 'No Label';
                    const questions = item.questions || [];

                    if (item.checked) {
                        return (
                            <div key={index} >
                                <h4 className='mt-4 mb-4' dangerouslySetInnerHTML={{ __html: label }} />
                                {questions.length > 0 && (
                                    <div >
                                        {questions.map((question, qIndex) => {
                                            const questionLabel = question.label || 'No Question Label';
                                            const options = question.options || [];

                                            return (
                                                <div key={qIndex} className='boxShadow p-2 mt-3'>
                                                    <div>Q . {questionLabel}</div>
                                                    {options.length > 0 && (
                                                        <div className='mt-3'>
                                                            {options.map((option, oIndex) => {
                                                                if (option.checked === 1) {
                                                                    return (
                                                                        <div key={oIndex} className='fw-bold'>
                                                                            {option.label}
                                                                        </div>
                                                                    );
                                                                } else {
                                                                    return null;
                                                                }
                                                            })}
                                                        </div>
                                                    )}

                                                    <div className='fw-bold mt-2'>Remarks</div>
                                                    <p>{question.remarks}</p>

                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        );
                    }

                } else if (item.type === "textarea") {

                    return (
                        <div className='boxShadow mt-3 mb-3 p-2'>

                            <div>{item.label}</div>
                            <div className='fw-bold mt-2'>{item.value || ''}</div>
                        </div>
                    )
                } else if (item.type === "sign") {

                    return (
                        <div className='boxShadow mt-2 mb-3 p-2 '>
                            <div>{item.label}</div>
                            <img src={`${STATIC_URL}/${item.value}`} width={100} />

                        </div>

                    )
                }
            })}
        </div>

    );
};

export default FormRender;
