import React, { useState, useEffect } from 'react';
import { Modal } from 'react-bootstrap';
import GalleryPage from "../apps/Gallery";
import { STATIC_URL } from "../constants";
import moment from 'moment'

const AuditSummary = ({ gmsThree, auditId }) => {
  console.log(gmsThree)
  const [showFindingListModal, setShowFindingListModal] = useState(false)
  const [selectedFindingListData, setSelectedFindingListData] = useState([]);
  const [gp, setGp] = useState([])
  const [offi, setOffi] = useState([])
  const [ncMinor, setNcMinor] = useState([])
  const [ncMajor, setNcMajor] = useState([])
  const [ncMedium, setNcMedium] = useState([])

  const [count, setCount] = useState({
    goodPractices: 0,
    majorNonConformances: 0,
    mediumNonConformances: 0,
    minorNonConformances: 0,
    opportunityForImprovement: 0
  });
  const handleCountClick = (data) => {



    setSelectedFindingListData(data);
    setShowFindingListModal(true);
  };

  const handleCloseModal = () => {
    setShowFindingListModal(false);
  };
  useEffect(() => {
    const summaryCounts = {
      goodPractices: 0,
      majorNonConformances: 0,
      mediumNonConformances: 0,
      minorNonConformances: 0,
      opportunityForImprovement: 0
    };
    let finding = []
    gmsThree.forEach(gmsThreeItem => {
      const filteredFindings = gmsThreeItem.auditFindings?.filter(i => i.auditId === auditId);
      if (filteredFindings && filteredFindings.length > 0) {
        finding.push(filteredFindings);
      }
    });

    let findingsForCurrentAudit = [];

    finding.forEach(item => {
      item.forEach(item1 => {
        findingsForCurrentAudit.push(item1)
      })
    })



    setGp(findingsForCurrentAudit ? findingsForCurrentAudit.filter(i => i.category === 'Good Practices') : [])
    setNcMajor(findingsForCurrentAudit ? findingsForCurrentAudit.filter(i => i.category === 'Non-Conformances' && i.classification === 'major') : [])
    setNcMinor(findingsForCurrentAudit ? findingsForCurrentAudit.filter(i => i.category === 'Non-Conformances' && i.classification === 'minor') : [])
    setOffi(findingsForCurrentAudit ? findingsForCurrentAudit.filter(i => i.category === 'Opportunity For Improvement') : [])
    setNcMedium(findingsForCurrentAudit ? findingsForCurrentAudit.filter(i => i.category === 'Non-Conformances' && i.classification === 'medium') : [])
    // gmsThree.forEach(gmsThreeItem => {
    //   const findingsForCurrentAudit = gmsThreeItem.auditFindings?.filter(i => i.auditId === auditId) || [];
    //   // console.log(auditId)
    //   // console.log(findingsForCurrentAudit)



    //   findingsForCurrentAudit.forEach(finding => {
    //     switch (finding.category) {
    //       case 'Good Practices':
    //         summaryCounts.goodPractices += 1;
    //         break;
    //       case 'Opportunity For Improvement':
    //         summaryCounts.opportunityForImprovement += 1;
    //         break;
    //       case 'Non-Conformances':
    //         if (finding.classification === 'major') {
    //           summaryCounts.majorNonConformances += 1;
    //         } else if (finding.classification === 'medium') {
    //           summaryCounts.mediumNonConformances += 1;
    //         } else if (finding.classification === 'minor') {
    //           summaryCounts.minorNonConformances += 1;
    //         }
    //         break;
    //       default:
    //         // Handle other categories if necessary
    //         break;
    //     }
    //   });
    // });

    // setCount(summaryCounts);
  }, [gmsThree, auditId]);

  const generateTableHeaders = (category) => {
    switch (category) {
      case 'Good Practices':
        return (
          <tr>
            <th>#</th>
            <th>Findings</th>
            <th>GMS Section</th>
            <th>Recommendations</th>
            <th>Uploads</th>

          </tr>
        );
      case 'Non-Conformances':
        return (
          <tr>
            <th>#</th>
            <th>Findings</th>
            <th>GMS Section</th>
            <th>Classification</th>
            <th>Potential Consequences</th>
            <th>Standards & References</th>
            <th>Recommended Mitigation Measures</th>
            <th>Target date for closure</th>
            <th>Action Assign Name</th>
            <th>Uploads</th>

          </tr>
        );
      case 'Opportunity For Improvement':
        return (
          <tr>
            <th>#</th>
            <th>Findings</th>
            <th>GMS Section</th>
            <th>Recommendations</th>
            <th>Standards & References</th>
            <th>Uploads</th>

          </tr>
        );
      default:
        return null;
    }
  };

  const generateTableRow = (item) => {
    const modifiedUploads = item.uploads ? item.uploads.map(i => {
      return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
    }) : []
    switch (item.category) {
      case 'Good Practices':
        return (
          <tr key={item.id}>
            <td>{item.maskId}</td>
            <td>{item.findings}</td>
            <td>{item.inspectionCategories.name}</td>
            <td>{item.recommendations}</td>
            <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>
          </tr>
        );
      case 'Non-Conformances':
        return (
          <tr key={item.id}>
            <td>{item.maskId}</td>
            <td>{item.findings}</td>
            <td>{item.inspectionCategories.name}</td>
            <td>{item.classification}</td>
            <td>{item.potentialHazard}</td>
            <td>{item.standardsAndReferences}</td>
            <td>{item.recommendations}</td>
            <td>{moment(item.dueDate, "YYYY-MM-DD").format("Do MMM YYYY")}</td>
            <td>{item.assignedTo?.firstName}</td>
            <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>

          </tr>
        );
      case 'Opportunity For Improvement':
        return (
          <tr key={item.id}>
            <td>{item.maskId}</td>
            <td>{item.findings}</td>
            <td>{item.inspectionCategories.name}</td>
            <td>{item.recommendations}</td>
            <td>{item.standardsAndReferences}</td>
            <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>

          </tr>
        );
      default:
        return null;
    }
  };

  return (
    <div>
      <div>Audit Summary</div>

      <div className="findings-container">
        <div className="finding">
          <p className="finding-title">Good Practices</p>
          <p className="finding-count" onClick={() => handleCountClick(gp)}>Number of Findings: <a href='#'>{gp.length}</a></p>
        </div>
        <div className="finding">
          <p className="finding-title">Opportunity for Improvement</p>
          <p className="finding-count" onClick={() => handleCountClick(offi)}>Number of Findings: <a href='#'>{offi.length}</a></p>
        </div>

        <div className="finding">
          <p className="finding-title">Minor Non-conformances</p>
          <p className="finding-count" onClick={() => handleCountClick(ncMinor)}>Number of Findings: <a href='#'>{ncMinor.length}</a></p>
        </div>
        <div className="finding">
          <p className="finding-title">Medium Non-conformances</p>
          <p className="finding-count" onClick={() => handleCountClick(ncMedium)}>Number of Findings: <a href='#'>{ncMedium.length}</a></p>
        </div>
        <div className="finding">
          <p className="finding-title">Major Non-conformances</p>
          <p className="finding-count" onClick={() => handleCountClick(ncMajor)}>Number of Findings:<a href='#'>{ncMajor.length}</a></p>
        </div>
      </div>


      <Modal show={showFindingListModal} size={'lg'} onHide={handleCloseModal} backdropClassName="nested-modal" centered>
        <Modal.Header closeButton>
          <Modal.Title>Details</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="table-responsive">
            <table className="table table-bordered table-striped">
              <thead>
                {selectedFindingListData.length > 0 && generateTableHeaders(selectedFindingListData[0].category)}
              </thead>
              <tbody>

                {selectedFindingListData.length > 0 && selectedFindingListData.map((item) => generateTableRow(item))}

              </tbody>
            </table>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <button onClick={handleCloseModal} className="btn btn-light">Close</button>
        </Modal.Footer>
      </Modal>

    </div>
  );


};

export default AuditSummary;
