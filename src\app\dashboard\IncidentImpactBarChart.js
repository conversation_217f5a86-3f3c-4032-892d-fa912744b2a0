import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import moment from "moment";
import { ALL_INCIDENT_URL } from "../constants";

const IncidentImpactBarChart = ({ dateRange, filterCriteria }) => {
    const [chartData, setChartData] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const params = {
                    include: [
                        { relation: "locationOne" },
                        { relation: "locationThree" },
                    ],
                };

                const response = await API.get(
                    `${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`
                );

                let incidents = response.data;


                console.log('Fetched all:', incidents, filterCriteria);

                incidents = incidents.filter(item => {
                    // Extract country from locationOne.name (text inside parentheses)
                    const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                    // Determine BU from locationThree.name
                    let bu = "Other";
                    const buName = item.locationThree?.name || "";

                    if (/Construction|Fitouts/i.test(buName)) {
                        bu = "Construction";
                    } else if (/DC|Data Center|Data Centre/i.test(buName)) {
                        bu = "DC";
                    } else if (/Office/i.test(buName)) {
                        bu = "Office";
                    }

                    // Now apply the filters
                    const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
                    const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
                    const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;

                    return isCountrySelected && isBUSelected && isSiteSelected;
                });


                const [startDate, endDate] = dateRange.map(date => moment(date));

                const impactCountsByMonth = {};

                incidents.forEach((incident) => {
                    // Exclude reported incidents
                    if (incident.status === 'Reported') return;

                    const incidentMoment = moment(incident.incidentDate, 'DD/MM/YYYY hh:mm A', true);
                    if (!incidentMoment.isValid()) return;

                    // Filter by dateRange
                    if (!incidentMoment.isBetween(startDate, endDate, null, '[]')) return;

                    const monthYear = incidentMoment.format('YYYY-MM');
                    const impactRaw = (incident.actualImpact || '').toLowerCase().trim();

                    // Normalize impact level
                    let impactLevel = null;
                    if (impactRaw.includes("near miss")) {
                        impactLevel = "Near-Miss";
                    } else if (impactRaw.includes("level 1")) {
                        impactLevel = "Level 1";
                    } else if (impactRaw.includes("level 2")) {
                        impactLevel = "Level 2";
                    } else if (impactRaw.includes("level 3")) {
                        impactLevel = "Level 3";
                    } else if (impactRaw.includes("level 4")) {
                        impactLevel = "Level 4";
                    } else if (impactRaw.includes("level 5")) {
                        impactLevel = "Level 5";
                    }





                    if (!impactLevel) return; // skip if no recognizable impact

                    if (!impactCountsByMonth[monthYear]) {
                        impactCountsByMonth[monthYear] = {
                            'Near-Miss': 0,
                            'Level 1': 0,
                            'Level 2': 0,
                            'Level 3': 0,
                            'Level 4': 0,
                            'Level 5': 0,
                        };
                    }

                    impactCountsByMonth[monthYear][impactLevel] += 1;
                });

                // Convert to sorted array
                const allMonths = [];
                let current = startDate.clone().startOf("month");

                while (current.isSameOrBefore(endDate, "month")) {
                    allMonths.push(current.format("YYYY-MM"));
                    current.add(1, "month");
                }

                // Ensure all months exist in the impactCountsByMonth map
                allMonths.forEach((monthYear) => {
                    if (!impactCountsByMonth[monthYear]) {
                        impactCountsByMonth[monthYear] = {
                            'Near-Miss': 0,
                            'Level 1': 0,
                            'Level 2': 0,
                            'Level 3': 0,
                            'Level 4': 0,
                            'Level 5': 0,
                        };
                    }
                });

                const formattedData = allMonths.map((monthYear) => ({
                    monthYear,
                    ...impactCountsByMonth[monthYear],
                }));

                // Format month-year labels
                const categories = formattedData.map(item =>
                    moment(item.monthYear, "YYYY-MM").format("MMM YYYY")
                );

                const categoryColors = {
                    "Near-Miss": "#4BC0C0",   // Teal
                    "Level 1": "#36A2EB",     // Blue
                    "Level 2": "#9966FF",     // Purple
                    "Level 3": "#FFCE56",     // Yellow
                    "Level 4": "#FF9F40",     // Orange
                    "Level 5": "#FF0000",     // Red (Critical)
                };

                const impactLevels = Object.keys(categoryColors);

                const impactLevelLabels = {
                    "Near-Miss": "Near-Miss",
                    "Level 1": "Level 1 (First Aid Incident - FAI)",
                    "Level 2": "Level 2 (Medical Treatment Incident - MTI)",
                    "Level 3": "Level 3 (Lost Time Incident - LTI)",
                    "Level 4": "Level 4 (High Severity Incident)",
                    "Level 5": "Level 5 (Critical Incident)",
                };

                const seriesData = impactLevels.map((level) => ({
                    name: impactLevelLabels[level],
                    data: formattedData.map((item) => item[level] || 0),
                    stack: "impact",
                    color: categoryColors[level],
                }));

                setChartData({ categories, series: seriesData });
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        fetchData();
    }, [dateRange]);

    const options = chartData
        ? {
            chart: {
                type: "column",
                zoomType: "xy",
            },
            title: { text: "" },
            xAxis: {
                categories: chartData.categories,
                title: { text: "Month-Year" },
                crosshair: true,
            },
            yAxis: {
                title: { text: "Incident Count" },
                min: 0,
            },
            tooltip: { shared: true },
            plotOptions: {
                column: {
                    stacking: "normal",
                    dataLabels: { enabled: true },
                },
            },
            legend: { enabled: true },
            series: chartData.series,
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            "downloadPNG",
                            "downloadJPEG",
                            "downloadPDF",
                            "downloadSVG",
                            "separator",
                            "downloadCSV",
                            "downloadXLS",
                        ],
                    },
                },
            },
        }
        : null;

    return <>{options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}</>;
};

export default IncidentImpactBarChart;
