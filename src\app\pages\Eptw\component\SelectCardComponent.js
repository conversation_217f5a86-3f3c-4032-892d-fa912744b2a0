import React, { useState, useEffect } from 'react';
import { Col, Card, Form } from 'react-bootstrap';
import Select from 'react-select';

function SelectCardComponent({
  mandatory,
  title,
  data,
  selectedValue,
  updateListSelected,
  type,
  label,
  searchList,
  box,
  error,
  disabled
}) {
  const [options, setOptions] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);

  // Prepare options for react-select
  useEffect(() => {
    const formattedOptions = data.map((item) => ({
      value: item.id,
      label: type === 'owner' ? item.firstName : item.name,
    }));
    setOptions(formattedOptions);

    // Set the selected option if there's a selectedValue
    if (selectedValue) {
      const selected = formattedOptions.find((option) => option.value === selectedValue);
      setSelectedOption(selected);
    }
  }, [data, selectedValue, type]);

  const handleChange = (option) => {
    setSelectedOption(option);
    const selectedItem = data.find((item) => item.id === option.value);
    updateListSelected(selectedItem, type);
  };

  return (
    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
      <Card style={box ? { boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' } : {}}>
        <Card.Body>
          <Form.Group controlId="formSelect">
            <Form.Label>
              {title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}
            </Form.Label>
            <Select
              value={selectedOption}
              onChange={handleChange}
              options={options}
              placeholder={label || 'Select...'}
              isSearchable
              isDisabled={disabled}
              styles={{
                control: (base) => ({
                  ...base,
                  borderColor: error ? "red" : "#dee2e6",
                  borderRadius: '10px',
                  boxShadow: 'none',
                  '&:hover': { borderColor: '#ced4da' },
                  backgroundColor: disabled ? "#e9ecef" : base.backgroundColor
                }),
              }}
            />
          </Form.Group>
        </Card.Body>
      </Card>
    </Col>
  );
}

export default SelectCardComponent;
