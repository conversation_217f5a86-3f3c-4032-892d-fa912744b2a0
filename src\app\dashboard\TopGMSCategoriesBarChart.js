import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import moment from "moment";
import { ALL_INCIDENT_URL } from "../constants";
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';

const TopGMSCategoriesBarChart = ({ dateRange, filterCriteria }) => {
  const impactLevels = [
  'Near-Miss',
  'Level 1 (First Aid Incident - FAI)',
  'Level 2 (Medical Treatment Incident - MTI)',
  'Level 3 (Lost Time Incident - LTI)',
  'Level 4 (High Severity Incident)',
  'Level 5 (Critical Incident)'
];
  const [chartData, setChartData] = useState(null);
  const [selectedImpacts, setSelectedImpacts] = useState(impactLevels);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const params = {
          include: [{ relation: "locationOne" }, { relation: "locationThree" }, { relation: "riskCategory" }],
        };

        const response = await API.get(
          `${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`
        );
        let incidents = response.data;




        incidents = incidents.filter(item => {
          // Extract country from locationOne.name (text inside parentheses)
          const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

          // Determine BU from locationThree.name
          let bu = "Other";
          const buName = item.locationThree?.name || "";

          if (/Construction|Fitouts/i.test(buName)) {
            bu = "Construction";
          } else if (/DC|Data Center|Data Centre/i.test(buName)) {
            bu = "DC";
          } else if (/Office/i.test(buName)) {
            bu = "Office";
          }

          // Normalize impact level
          const impactRaw = (item.actualImpact || '').toLowerCase().trim();
          let impactLevel = null;
          if (impactRaw.includes("near miss")) {
              impactLevel = "Near-Miss";
          } else if (impactRaw.includes("level 1")) {
              impactLevel = "Level 1";
          } else if (impactRaw.includes("level 2")) {
              impactLevel = "Level 2";
          } else if (impactRaw.includes("level 3")) {
              impactLevel = "Level 3";
          } else if (impactRaw.includes("level 4")) {
              impactLevel = "Level 4";
          } else if (impactRaw.includes("level 5")) {
              impactLevel = "Level 5";
          }

          // Now apply the filters
          const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
          const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
          const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;
          const isImpactSelected = selectedImpacts.length === 0 || 
              (impactLevel && selectedImpacts.includes(impactLevel));

          return isCountrySelected && isBUSelected && isSiteSelected && isImpactSelected;
        });

        const [startDate, endDate] = dateRange.map((date) => moment(date));

        // Step 1: Filter by date range using incidentDate
        const filteredIncidents = incidents.filter((incident) => {
          const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A", true);
          return (
            incidentMoment.isValid() &&
            incidentMoment.isBetween(startDate, endDate, null, "[]")
          );
        });

        // Step 2: Group by month → GMS category → count
        const gmsCountsByMonth = {};

        filteredIncidents.forEach((incident) => {
          const date = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A");
          const monthYear = date.format("YYYY-MM");
          const gms = incident.riskCategory?.name || "Unknown";

          if (!gmsCountsByMonth[monthYear]) {
            gmsCountsByMonth[monthYear] = {};
          }

          if (!gmsCountsByMonth[monthYear][gms]) {
            gmsCountsByMonth[monthYear][gms] = 0;
          }

          gmsCountsByMonth[monthYear][gms] += 1;
        });

        // Generate full list of months between startDate and endDate
        const allMonths = [];
        let current = startDate.clone().startOf("month");
        while (current.isSameOrBefore(endDate, "month")) {
          allMonths.push(current.format("YYYY-MM"));
          current.add(1, "month");
        }

        // Ensure every month exists in gmsCountsByMonth
        allMonths.forEach((monthYear) => {
          if (!gmsCountsByMonth[monthYear]) {
            gmsCountsByMonth[monthYear] = {};
          }
        });

        // Step 3: Prepare formatted data
        const formattedData = allMonths.map((monthYear) => {
          const topGMS = Object.entries(gmsCountsByMonth[monthYear])
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([gms, count]) => ({ gms, count }));

          return {
            monthYear,
            gmsCategories: topGMS,
          };
        });

        // Step 4: Labels for xAxis
        const labels = allMonths.map((monthYear) =>
          moment(monthYear, "YYYY-MM").format("MMM YYYY")
        );


        // Step 5: Get all unique GMS categories across months
        const allGmsCategories = [
          ...new Set(
            formattedData.flatMap((item) =>
              item.gmsCategories.map((gms) => gms.gms)
            )
          ),
        ];

        // Custom Colors (your palette)
        const customColors = [
          "#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#E91E63",
          "#795548", "#00BCD4", "#FFEB3B", "#FF5722", "#607D8B",
          "#673AB7", "#3F51B5", "#8BC34A", "#CDDC39", "#F44336",
          "#03A9F4", "#FFB300", "#D32F2F", "#388E3C", "#7B1FA2"
        ];

        // Step 6: Build series for Highcharts
        const seriesData = allGmsCategories.map((gms, index) => {
          const dataPoints = formattedData.map((monthData) => {
            const found = monthData.gmsCategories.find((cat) => cat.gms === gms);
            return found ? found.count : 0;
          });

          return {
            name: gms,
            data: dataPoints,
            color: customColors[index % customColors.length],
            stack: "gmsStack",
          };
        });

        setChartData({ labels, series: seriesData });
      } catch (error) {
        console.error("Error fetching GMS category data:", error);
      }
    };

    fetchData();
  }, [dateRange, selectedImpacts]);

  // Highcharts Configuration
  const options = chartData
    ? {
      chart: {
        type: "column",
        zoomType: "xy",
      },
      title: { text: "" },
      xAxis: {
        categories: chartData.labels,
        title: { text: "Month-Year" },
        crosshair: true,
      },
      yAxis: {
        min: 0,
        title: { text: "Category Count" },
        stackLabels: {
          enabled: true,
        },
      },
      tooltip: {
        shared: true,
        pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
      },
      plotOptions: {
        column: {
          stacking: "normal",
          dataLabels: {
            enabled: true,
          },
        },
      },
      legend: {
        layout: "vertical",
        align: "right",
        verticalAlign: "middle",
        itemMarginBottom: 5,
        navigation: {
          enabled: true,
        },
      },
      series: chartData.series,
      exporting: {
        enabled: true,
        buttons: {
          contextButton: {
            menuItems: [
              "downloadPNG",
              "downloadJPEG",
              "downloadPDF",
              "downloadSVG",
              "separator",
              "downloadCSV",
              "downloadXLS",
            ],
          },
        },
      },
    }
    : null;

  const handleImpactChange = (value) => {
    setSelectedImpacts(value);
  };

  return (
    <div>
      <div style={{ marginBottom: '20px' }}>
        <MultiSelect
          value={selectedImpacts}
          options={impactLevels}
          onChange={(e) => handleImpactChange(e.value)}
          placeholder="Select Impact Levels"
          style={{ width: '50%' }}
          display="chip"
        />
      </div>
      {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
    </div>
  );
};

export default TopGMSCategoriesBarChart;
