import React, { useState,useEffect } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL } from "../../constants";
const OpportunityForImprovementForm = ({ gms, aformData, handleChange, handleFileChange }) => {
    // Form fields for Area For Improvement

    const [file, setFile] = useState([])

    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };
    
    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [])


    return (
        <form>
            <h2>Opporunity For Improvement Form</h2>
            {/* Add your form fields here */}

            <div className="mb-3">
                <label htmlFor="inspectionCategories" className="form-label">
                    GMS Section
                </label>
                <select

                    className="form-control"
                    id="inspectionCategories"
                    name="inspectionCategories"
                    value={aformData.inspectionCategories.id}
                    onChange={handleChange}

                >
                    <option value="">Choose GMS Section</option>
                    {
                        gms.map(i => (<option key={i.id} value={i.id}>{i.name}</option>))
                    }
                </select>
            </div>

            <div className="mb-3">
                <label htmlFor="findings" className="form-label">
                    Findings:
                </label>
                <textarea
                    className="form-control"
                    id="findings"
                    name="findings"
                    value={aformData.findings}
                    onChange={handleChange}
                ></textarea>
            </div>
            <div className="mb-3">
                <label htmlFor="evidencePhotos" className="form-label">
                    Upload Photos (Evidence)
                </label>
                {file.length != 0 &&
                    <DropzoneArea
                        initialFiles={file}
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
                {file.length === 0 &&
                    <DropzoneArea
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
            </div>
            <div className="mb-3">
                <label htmlFor="standardsAndReferences" className="form-label">
                    Standards & References:
                </label>
                <input
                    type="text"
                    className="form-control"
                    id="standardsAndReferences"
                    name="standardsAndReferences"
                    value={aformData.standardsAndReferences}
                    onChange={handleChange}
                />
            </div>

            <div className="mb-3">
                <label htmlFor="recommendations" className="form-label">
                    Opportunity for Improvement:
                </label>
                <textarea
                    className="form-control"
                    id="recommendations"
                    name="recommendations"
                    value={aformData.recommendations}
                    onChange={handleChange}
                ></textarea>
            </div>
        </form>
    );
};

export default OpportunityForImprovementForm;