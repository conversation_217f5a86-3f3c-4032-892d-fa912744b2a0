import React, { Component, useState, useEffect } from 'react'

import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import PropTypes from 'prop-types';
import CardOverlay from './CardOverlay';
import Permit from './Permit';
import { ACTION_URL, PERMIT_REPORTS, ALL_PERMITS_URL } from '../constants';
import API from '../services/API';
import FilterLocation from './FilterLocation';
import LineChart from '../dashboard/LineChart';
import PolarChart from '../dashboard/PolarChart';
import SatelliteMap from './SatelliteMap';
import AllFilterLocation from './AllLocationFilter';
import AllPermits from './AllPermits';
import ActionCard from './ActionCard';
import AppSwitch from './AppSwitch';
import AllFilterLocationVertical from './AllFilterLocationVertical'
import { InputText } from 'primereact/inputtext';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import moment from 'moment'
import Typography from '@mui/material/Typography'
import Actions from './Eptw/Actions';
// This will log true if the role exists, false otherwise.
import { useSelector } from 'react-redux';
import { Button } from 'primereact/button';
import DcOpsForm from "./Eptw/DcOpsForm";
import Construction from "./Eptw/Construction";
function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`eptw-tabpanel-${index}`}
      aria-labelledby={`eptw-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{}}>
          {children}
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `eptw-tab-${index}`,
    'aria-controls': `eptw-tabpanel-${index}`,
  };
}
const customFontStyle = {
  fontFamily: 'Lato, sans-serif',
  display: "flex",
  alignItems: 'center',
  justifyContent: 'center'
};

const Eptw = () => {
  const dates9 = [
    [new Date("2023-07-01").getTime(), 42000000],
    [new Date("2023-07-02").getTime(), 65000000],
    [new Date("2023-07-03").getTime(), 78000000],
    [new Date("2023-07-04").getTime(), 46000000],
    [new Date("2023-07-05").getTime(), 56000000],
    [new Date("2023-07-06").getTime(), 87000000],
    [new Date("2023-07-07").getTime(), 78000000],
    [new Date("2023-07-08").getTime(), 65000000],
    [new Date("2023-07-09").getTime(), 62000000],
    [new Date("2023-07-10").getTime(), 84000000],
  ];
  const [value, setValue] = useState(0);
  const [topLevelValue, setTopLevelValue] = useState(0);
  const [locationOneId, setlocationOneId] = useState('')
  const [locationTwoId, setlocationTwoId] = useState('')
  const [locationThreeId, setlocationThreeId] = useState('')
  const [locationFourId, setlocationFourId] = useState('')
  const [dcshow, setDcshow] = useState(false)
  const [conshow, setConshow] = useState(false)
  const me = useSelector((state) => state.login.user);
  const hasPTWApplicantRole = me?.validationRoles?.some((item) => item.name === 'PTW Applicant');

  console.log(hasPTWApplicantRole);
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleTopLevelChange = (event, newValue) => {
    setTopLevelValue(newValue);
  };




  const [actionData, setActionData] = useState([]);
  const [rendered, setRendered] = useState(0)
  const [permits, setPermits] = useState([])
  const [filterData, setFilterData] = useState([])
  const [showFilter, setShowFilter] = useState(true)
  const [applyFilter, setApplyFilter] = useState(false)
  const [startDate, setStartDate] = useState(null)
  const [endDate, setEndDate] = useState(null)
  const [clear, setClear] = useState(true)
  const [search, setSearch] = useState('')

  const [filter, setFilter] = useState([])
  const [finalPermit, setFinalPermit] = useState([])

  const [dates, setDates] = useState([])

  useEffect(() => {

    getActionData('PermitToWork', ['open', 'returned', 'pending_normalization']);
  }, [])
  const getActionData = async (applicationType, statusList) => {
    try {
      const response = await API.get(ACTION_URL);
      if (response.status === 200) {
        const filteredData = response.data.filter(action =>
          action.application === applicationType && statusList.includes(action.status)
        );
        setActionData(filteredData);
      }
    } catch (error) {
      console.error('Error fetching action data:', error);
    }
  };



  useEffect(() => {

    getActionData('PermitToWork', ['open', 'returned', 'pending_normalization'])
  }, [
    rendered
  ])

  useEffect(() => {
    getPermits();
  }, [])
  const getPermits = async () => {
    const response = await API.get(ALL_PERMITS_URL);
    if (response.status === 200) {

      const data = response.data.map(item => {

        // Update the permitType
        item.permitType = item.permitType === 'CA' ? 'Construction' : item.permitType;

        // Format the created date
        item.created = moment(item.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY');

        // Compare permitStartDate with local datetime and update the status


        if (item.status === 'Active') {
          const permitStartDate = moment(item.permitStartDate, 'DD-MM-YYYY hh:mm A');
          const currentTime = moment();

          if (permitStartDate.isSameOrAfter(currentTime)) {
            item.status = 'Active';
          } else {
            item.status = 'Pending Work Commencement';
          }
        }

        return item;
      });

      setPermits([...data].reverse());
      setFilterData([...data].reverse());

      console.log(data);
    }
  };


  const getFilteredActions = (applicationType, statusList) => {
    return actionData.filter(action =>
      action.application === applicationType && statusList.includes(action.status)
    );
  }

  const isBetweenDateRange = (dateString, date1, date2) => {
    // Parse the date strings using Moment.js
    const date = moment(dateString, ['DD-MM-YYYY HH:mm', 'ddd MMM DD YYYY HH:mm:ss ZZ', 'DD-MM-YYYY hh:mm A', 'Do MMM YYYY', moment.ISO_8601]);

    // Check if the parsed date is between date1 and date2
    return date.isBetween(date1, date2, null, '[]'); // Use square brackets to include both ends of the range
  }
  const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate) => {
    setDates([])
    const data = [
      locationOneId.name || '',
      locationTwoId.name || '',
      locationThreeId.name || '',
      locationFourId.name || '',

    ];

    if (startDate !== null && endDate !== null) {
      const date = [
        moment(startDate).format('MMM YYYY'),
        moment(endDate).format('MMM YYYY')
      ]
      setDates(date)
    }


    setFilter(data)
    setSearch('')

    setlocationOneId(locationOneId.id)
    setlocationTwoId(locationTwoId.id)
    setlocationThreeId(locationThreeId.id)
    setlocationFourId(locationFourId.id)
    setStartDate(startDate)
    setEndDate(endDate)
  };
  useEffect(() => {


    if (!applyFilter) {
      setPermits(filterData.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))

    } else {
      setPermits(finalPermit.filter(item => item.maskId.toLowerCase().includes(search.toLowerCase())))

    }

  }, [search])
  useEffect(() => {

    const filterData1 = (data) => {
      return data.filter(item => {
        return (
          (!locationOneId || item.locationOneId === locationOneId) &&
          (!locationTwoId || item.locationTwoId === locationTwoId) &&
          (!locationThreeId || item.locationThreeId === locationThreeId) &&
          (!locationFourId || item.locationFourId === locationFourId) &&
          (!startDate || !endDate || isBetweenDateRange(item.created, moment(startDate).startOf('month'), moment(endDate).endOf('month')))

        );
      });
    };

    setPermits(filterData1(filterData));
    setFinalPermit(filterData1(filterData))


  }, [locationOneId, locationTwoId, locationThreeId, locationFourId, startDate, endDate])
  const onApplyFilter = (type) => {
    setApplyFilter(type)
    setShowFilter(true)
  }

  const onCancelFilter = (type) => {
    setApplyFilter(false)
    setShowFilter(true)
    setClear(!clear)
  }

  return (
    <>

      <CardOverlay>
        <AppSwitch value={{ label: 'ePermit to Work', value: 'eptw' }} />

        <div className='row'>

          <div className='col-12 mb-4'>
            <div className='d-flex align-items-center'>
              <div className='col-1 d-flex'>
                {!applyFilter ?
                  <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={showFilter ? {} : { background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                    <div className='d-flex flex-column align-items-end'>
                      <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                      <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                    </div>
                    <i className='pi pi-filter ms-2' style={{ fontSize: 22 }} />
                  </div>
                  :
                  <div className='p-2 p-card me-3 d-flex align-items-center justify-content-between' style={{ background: '#73bdf052' }} onClick={() => setShowFilter(!showFilter)}>
                    <div className='d-flex flex-column align-items-end'>
                      <i className='pi pi-arrow-left' style={{ fontSize: 10 }} />
                      <i className='pi pi-arrow-left' style={{ fontSize: 15 }} />
                    </div>
                    <i className='pi pi-filter-slash ms-2' style={{ fontSize: 22 }} />

                  </div>
                }

              </div>
              <div className='col-7 d-flex'>
                {applyFilter && <>
                  {filter.length !== 0 &&
                    <h5><b>Location : </b>{filter.map((location, index) => (
                      location !== '' &&
                      <React.Fragment key={index}>
                        <span className='loc-box'>{location}</span>
                        {index < filter.length - 1 && <i className="pi pi-chevron-right me-1 ms-1"></i>}
                      </React.Fragment>
                    ))}</h5>
                  }
                  {dates.length !== 0 &&
                    <h5 className='ms-3'><b>Month Range :</b> {dates.map((location, index) => (
                      <React.Fragment key={index}>
                        <span className='loc-box'>{location}</span>
                        {index < dates.length - 1 && " To "}
                      </React.Fragment>
                    ))}</h5>
                  }
                </>}
              </div>
              <div className='col-6'>

                {hasPTWApplicantRole && <>
                  <Button label="Apply Construction Permit" className="p-button me-3" onClick={() => setConshow(true)} />
                  <Button label="Apply DC Permit" className="p-button " onClick={() => setDcshow(true)} />
                </>}
                {/* <div className="p-input-icon-left ">
                  <i className="fa fa-search" />
                  <InputText type="search" style={{ borderRadius: 8 }} placeholder='Search' onChange={(e) => setSearch(e.target.value)} />
                </div> */}
              </div>
            </div>
          </div>
          <div className={'col-3'} style={{ paddingRight: 0 }} hidden={showFilter}>
            <AllFilterLocationVertical handleFilter={handleFilter} disableAll={false} period={true} onApplyFilter={onApplyFilter} onCancelFilter={onCancelFilter} />
          </div>
          <div className={!showFilter ? 'col-9' : 'col-12'}>
            <Box sx={{ width: '100%' }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={value} onChange={handleChange} aria-label="eptw report table">
                  <Tab label={<Typography variant="body1" style={customFontStyle}>
                    My Actions <span className='headerCount'>{actionData.length}</span>
                  </Typography>} {...a11yProps(0)} />
                  <Tab label={
                    <Typography variant="body1" style={customFontStyle}>
                      All Permits <span className='headerCount'>{permits.length}</span>
                    </Typography>
                  } {...a11yProps(1)} />
                </Tabs>
              </Box>
              <CustomTabPanel value={value} index={0}>
                <Actions action={actionData} applicationType="PermitToWork" setRendered={setRendered} />

              </CustomTabPanel>
              <CustomTabPanel value={value} index={1}>
                <AllPermits data={permits} />
              </CustomTabPanel>
              <CustomTabPanel value={value} index={2}>
                <Permit status={'Active: Timed Out'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={3}>
                <Permit status={'Pending Approval'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={4}>
                <Permit status={'Revoked'} />
              </CustomTabPanel>

              <CustomTabPanel value={value} index={5}>
                <Permit status={'Inactive: Timed Out'} />
              </CustomTabPanel>

            </Box>
          </div>
        </div>

        {dcshow &&
          <DcOpsForm show={dcshow} handleClose={() => setDcshow(false)} />
        }
        {conshow &&
          <Construction show={conshow} handleClose={() => setConshow(false)} />
        }


      </CardOverlay>
    </>
  )
}

export default Eptw
