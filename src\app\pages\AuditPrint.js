
import moment from 'moment'
import { coverPage, logo } from './Audit/Base64Images';
import htmlToPdfmake from 'html-to-pdfmake';

async function convertImageToBase64(url) {
    const response = await fetch(url);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}




export const AuditPrint = async (data) => {
    const summaryCounts = {
        goodPractices: 0,
        majorNonConformances: 0,
        mediumNonConformances: 0,
        minorNonConformances: 0,
        opportunityForImprovement: 0,
    };
    const scoreCellStyle = {
        fillColor: data.overallScoresAndColors?.color || 'gray',
        color: data.overallScoresAndColors?.color === '#e29e09' ? 'black' : 'white', // Set font color to white
        alignment: 'center', // Center-align text
        margin: [0, 10, 0, 10], // Adjust margins as needed
    };

    const gmsLabelStyle = {
        fontSize: 10,

        margin: [0, 5, 0, 5],
    };

    const gmsContentStyle = {
        fontSize: 10,
        margin: [0, 5, 0, 5],
    };

    const getClassificationStyle = (classification) => {
        switch (classification.toLowerCase()) {
            case 'major':
                return { color: '#58595b', bold: true };
            case 'medium':
                return { color: 'orange', bold: true };
            case 'minor':
                return { color: 'gold', bold: true }; // A darker shade of yellow for better legibility
            default:
                return { color: 'black', bold: false };
        }
    };

    data.allGmsThree.forEach(gmsThreeItem => {
        const findingsForCurrentAudit = gmsThreeItem.auditFindings?.filter(i => i.auditId === data.audit.id) || [];

        findingsForCurrentAudit.forEach(finding => {
            switch (finding.category) {
                case 'Good Practices':
                    summaryCounts.goodPractices += 1;
                    break;
                case 'Opportunity For Improvement':
                    summaryCounts.opportunityForImprovement += 1;
                    break;
                case 'Non-Conformances':
                    if (finding.classification === 'major') {
                        summaryCounts.majorNonConformances += 1;
                    } else if (finding.classification === 'medium') {
                        summaryCounts.mediumNonConformances += 1;
                    } else if (finding.classification === 'minor') {
                        summaryCounts.minorNonConformances += 1;
                    }
                    break;
                default:
                    // Handle other categories if necessary
                    break;
            }
        });
    });

    const getGmsFourRows = (gmsThree) => {
        const gp = gmsThree.auditFindings ? gmsThree.auditFindings.filter(i => i.category === 'Good Practices' && i.auditId === data.audit.id) : [];
        const nc = gmsThree.auditFindings ? gmsThree.auditFindings.filter(i => i.category === 'Non-Conformances' && i.auditId === data.audit.id) : [];
        const ofi = gmsThree.auditFindings ? gmsThree.auditFindings.filter(i => i.category === 'Opportunity For Improvement' && i.auditId === data.audit.id) : [];


        let rows = [];

        for (const gpItem of gp) {
            const stackItems = [];

            const addStackItem = (label, value) => {
                if (value) {
                    stackItems.push(
                        { text: `${label}:`, bold: true, margin: [0, 5, 0, 0] }, // Label with bold style and margin
                        { text: value, margin: [0, 0, 0, 0] } // Value with no additional margin
                    );
                }
            };

            // Add stack items
            addStackItem('Category', gpItem.category);
            addStackItem('Classification', gpItem.classification);
            addStackItem('Findings', gpItem.findings);
            addStackItem('GMS Section', gpItem.inspectionCategories?.name);
            addStackItem('Potential Hazard', gpItem.potentialHazard);
            addStackItem('Recommendations', gpItem.recommendations);
            addStackItem('Standards and References', gpItem.standardsAndReferences);
            addStackItem('Time Frame', gpItem.timeFrame);

            let img = '';
            // Check for image and add if available
            if (gpItem.uploads && gpItem.uploads.length > 0) {
                // Assuming the upload is a valid image path or base64 string
                img = { image: gpItem.uploads[0], width: 250 };
            }

            // Push the table to the rows array
            rows.push([{
                table: {
                    widths: ['50%', '50%'],
                    body: [
                        [
                            img, // Image cell
                            {
                                stack: stackItems, // Stack of text items
                            }
                        ]
                    ]
                },
                layout: 'noBorders',
                colSpan: 2
            }, {}]);
        }


        for (const ncItem of nc) {
            const stackItems = [];

            const addStackItem = (label, value, style) => {
                if (value) {
                    stackItems.push(
                        { text: `${label}:`, bold: true, margin: [0, 5, 0, 0] }, // Label with bold style and margin
                        { text: value, ...style, margin: [0, 0, 0, 0] } // Value with no additional margin
                    );
                }
            };

            // Add stack items
            addStackItem('Category', ncItem.category);
            addStackItem('Classification', ncItem.classification.charAt(0).toUpperCase() + ncItem.classification.slice(1), getClassificationStyle(ncItem.classification));
            addStackItem('Findings', ncItem.findings);
            addStackItem('GMS Section', ncItem.inspectionCategories?.name);
            addStackItem('Potential Hazard', ncItem.potentialHazard);
            addStackItem('Recommendations', ncItem.recommendations);
            addStackItem('Standards and References', ncItem.standardsAndReferences);
            addStackItem('Time Frame', ncItem.timeFrame);

            let img = '';
            // Check for image and add if available
            if (ncItem.uploads && ncItem.uploads.length > 0) {
                // Assuming the upload is a valid image path or base64 string
                img = { image: ncItem.uploads[0], width: 250 };
            }

            // Push the table to the rows array
            rows.push([{
                table: {
                    widths: ['50%', '50%'],
                    body: [
                        [
                            img, // Image cell
                            {
                                stack: stackItems, // Stack of text items
                            }
                        ]
                    ]
                },
                layout: 'noBorders',
                colSpan: 2
            }, {}]);
        }


        for (const ofiItem of ofi) {
            let img = '';
            const stackItems = [];

            const addStackItem = (label, value) => {
                if (value) {
                    stackItems.push(
                        { text: `${label}:`, bold: true, margin: [0, 5, 0, 0] }, // Label with bold style and margin
                        { text: value, margin: [0, 0, 0, 0] } // Value with margin
                    );
                }
            };

            addStackItem('Category', ofiItem.category);
            addStackItem('Classification', ofiItem.classification);
            addStackItem('GMS Section', ofiItem.inspectionCategories?.name);
            addStackItem('Findings', ofiItem.findings);
            addStackItem('Potential Hazard', ofiItem.potentialHazard);
            addStackItem('Recommendations', ofiItem.recommendations);
            addStackItem('Standards and References', ofiItem.standardsAndReferences);
            addStackItem('Time Frame', ofiItem.timeFrame);


            // const img = await convertImageToBase64(ofiItem.uploads[0])
            if (ofiItem.uploads && ofiItem.uploads.length > 0) {
                img = { image: ofiItem.uploads[0], width: 250 };
            }
            rows.push([{
                table: {
                    widths: ['50%', '50%'],
                    body: [[

                        img
                        ,
                        {
                            stack: stackItems,
                        }
                    ]]
                },
                layout: 'noBorders',
                colSpan: 2
            }]);
        };


        return rows;
    };

    const getGmsThreeRows = (gmsThreeData) => {
        return gmsThreeData.flatMap(gmsThree => {

            if (data.dropdownSelections?.[gmsThree.id] && parseInt(data.dropdownSelections[gmsThree.id]) !== 0) {
                const notesText = data.notesReport?.[gmsThree.id] && data.notesReport[gmsThree.id] !== "Click to add notes..." ? data.notesReport[gmsThree.id] : "";

                const gmsThreeRow = [
                    {
                        text: [
                            { text: gmsThree.name, style: 'gmsLabel' },
                            { text: '\n' }, // Line break
                            { text: notesText, style: 'notesStyle' }
                        ]
                    },
                    { text: data.dropdownSelections[gmsThree.id] || 0, style: 'scoreValueBlack' },
                ];

                const gmsFourRows = getGmsFourRows(gmsThree);
                return [gmsThreeRow, ...gmsFourRows];
            } else {
                return []
            }
        });
    };
    // Function to generate rows for gmsTwo and its gmsThree children
    const getGmsTwoRows = (gmsTwoData, gmsThreeData) => {
        return gmsTwoData.flatMap(gmsTwo => {
            const gmsTwoScoreAndColor = data.gmsTwoScoresAndColors[gmsTwo.id];
            const gmsTwoRow = [
                { text: gmsTwo.name, style: 'gmsSubLabel' },
                { text: gmsTwoScoreAndColor && gmsTwoScoreAndColor.score > 0 ? gmsTwoScoreAndColor.score.toFixed(2) : 'NA/NR', style: 'scoreValue', fillColor: gmsTwoScoreAndColor?.color || 'gray', color: gmsTwoScoreAndColor?.color === '#e29e09' ? 'black' : 'white' },
            ];

            const gmsThreeRows = getGmsThreeRows(gmsThreeData.filter(gmsThree => gmsThree.auditGmsTwoId === gmsTwo.id));
            return [gmsTwoRow, ...gmsThreeRows];
        });
    };

    // Generating the complete table content
    const tableContent = data.allGmsOne.flatMap(gmsOne => {
        const gmsOneScoreAndColor = data.gmsOneScoresAndColors[gmsOne.id];
        const gmsOneRow = [
            { text: gmsOne.name || '', style: 'gmsHeading', pageBreak: 'before' },
            { text: gmsOneScoreAndColor && gmsOneScoreAndColor.score > 0 ? gmsOneScoreAndColor.score.toFixed(2) : 'NA/NR', style: 'scoreValue', fillColor: gmsOneScoreAndColor?.color || 'gray', color: gmsOneScoreAndColor?.color === '#e29e09' ? 'black' : 'white' }
            // Additional columns (ensure these are present in all rows)
        ];

        const gmsTwoData = data.allGmsTwo.filter(gmsTwo => gmsTwo.auditGmsOneId === gmsOne.id);
        const gmsTwoRows = getGmsTwoRows(gmsTwoData, data.allGmsThree);

        return [gmsOneRow, ...gmsTwoRows];
    });

    const gmsOneRow = data.allGmsOne.map(gmsOne => {
        const gmsOneScoreAndColor = data.gmsOneScoresAndColors[gmsOne.id];
        return {
            text: gmsOne.name || '',
            style: { color: gmsOneScoreAndColor?.color || 'black', bold: true }, // Apply the color style from gmsOneScoreAndColor, default to green if not present
        };
    });
    const gmsOneContent = [
        ...gmsOneRow,
        // ... Add other cell objects here if needed for additional data
    ];

    function setFontSize(content) {
        if (Array.isArray(content)) {
            content.forEach(item => setFontSize(item));
        } else if (typeof content === 'object' && content !== null) {
            content.fontSize = 10; // Set font size to 10
            if (content.stack) {
                setFontSize(content.stack);
            }
            if (content.ul) {
                setFontSize(content.ul);
            }
            if (content.ol) {
                setFontSize(content.ol);
            }
            if (content.table) {
                setFontSize(content.table.body);
            }
        }
    }

    const documentDefinitionData = {
        pageSize: 'A4',
        pageMargins: [40, 40, 40, 40],
        header: function (currentPage, pageCount) {

            if (currentPage !== 1) {

                return {
                    columns: [
                        {},
                        {
                            // Assuming you have the logo in base64 format or as an accessible URL
                            image: logo,
                            width: 100, // Adjust logo width as needed
                            alignment: 'right',
                            margin: [20, 0, 0, 0], // Adjust margins as needed
                            border: [false, false, false, false] // Remove cell borders
                        }
                    ],
                    margin: [20, 10, 20, 0] // Adjust margins as needed
                };
            }
        },
        content: [

            {
                stack: [
                    {
                        image: coverPage,
                        fit: [595, 842], // Fit the image to A4 dimensions
                        pageBreak: 'after', // Ensure a page break after the cover image
                        margin: [-40, -40, 0, 0]
                    },

                ]
            },


            {
                table: {
                    widths: ['100%'],
                    body: [
                        [{ text: 'General Information', style: 'header' }]
                    ]
                },
                layout: 'noBorders' // This removes the table borders
            },
            {
                style: 'tableExample',
                table: {
                    widths: ['25%', '25%', '25%', '25%'],
                    body: [[
                        'Project / DC Operation',
                        `${data.audit.locationOne.name} > ${data.audit.locationTwo.name} > ${data.audit.locationThree.name} > ${data.audit.locationFour.name}`,
                        'Country Head of BU',
                        data.audit.headerInformation?.countryHeadOfBU || ''
                    ],
                    [
                        'Project / DC Leader',
                        data.audit.headerInformation?.projectDcLeader || '',
                        'Country Head of EHS',
                        data.audit.headerInformation?.countryHeadOfEHS || ''
                    ],
                    [
                        'Assurance / EHS Lead',
                        '', // Replace with the actual field name
                        'Date of Audit',
                        `${moment(data.audit.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")} - ${moment(data.audit.endDateTime, "DD/MM/YYYY").format("Do MMM YYYY")}`
                    ],
                    [
                        'Audit Team Member(s)',
                        data.audit.headerInformation?.auditTeamMembers || '',
                        'Auditees',
                        data.audit.headerInformation?.auditAttendees || ''
                    ]
                    ]
                }
            },
            {
                table: {
                    widths: ['100%'],
                    body: [
                        [{ text: 'Project Description', style: 'header' }]
                    ]
                },
                layout: 'noBorders' // This removes the table borders
            },
            {
                text: `${data.audit.locationFour.description ? data.audit.locationFour.description : ''}`

            },
            '\n\n',
            {
                table: {
                    widths: ['*'], // Single column
                    body: [
                        [
                            {
                                text: `Overall Score: ${data.overallScoresAndColors?.score || '0'}`, // Include "+" sign
                                style: 'overallScoreLabel', // Apply style to the entire cell
                            },
                        ],
                    ],
                },
                layout: {
                    defaultBorder: false, // Remove default cell borders
                },
                style: 'scoreCell', // Apply the custom style
            },


            {
                table: {
                    widths: ['100%'],
                    body: [
                        [{ text: 'Overall Assessment', style: 'header' }]
                    ]
                },
                layout: 'noBorders' // This removes the table borders
            },
            htmlToPdfmake(data.overall),
            '\n\n',
            {
                table: {
                    widths: ['100%'],
                    body: [
                        [{ text: 'Key Findings', style: 'header' }]
                    ]
                },
                layout: 'noBorders'
            },
            htmlToPdfmake(data.keyFindings),
            '\n\n',
            {
                table: {
                    widths: ['100%'],
                    body: [
                        [{ text: 'Summary of Audit', style: 'header' }]
                    ]
                },
                layout: 'noBorders'
            },

            {
                style: 'tableExample',

                table: {
                    widths: ['50%', '50%'], // Set each column width to 50%
                    body: [
                        [
                            { text: 'Categories', style: 'tableHeader' },
                            { text: 'No. of Findings', style: 'tableHeader' }
                        ],
                        [
                            { text: 'Good Practices', fillColor: '#c5dfb3' },
                            { text: summaryCounts.goodPractices.toString().padStart(2, '0'), fillColor: '#c5dfb3' }
                        ],
                        [
                            { text: 'Opportunity For Improvement', fillColor: '#d9d9d9' },
                            { text: summaryCounts.opportunityForImprovement.toString().padStart(2, '0'), fillColor: '#d9d9d9' }
                        ],
                        [
                            { text: 'Minor Non-Conformances', fillColor: '#ffff00' },
                            { text: summaryCounts.minorNonConformances.toString().padStart(2, '0'), fillColor: '#ffff00' }
                        ],

                        [
                            { text: 'Major Non-Conformances', fillColor: '#58595b', color: 'white' },
                            { text: summaryCounts.majorNonConformances.toString().padStart(2, '0'), fillColor: '#58595b', color: 'white' }
                        ],
                        [
                            { text: 'Medium Non-Conformances', fillColor: '#ff9f00' },
                            { text: summaryCounts.mediumNonConformances.toString().padStart(2, '0'), fillColor: '#ff9f00' }
                        ],
                    ],
                },
            },

            { text: '', pageBreak: 'before' },
            {
                table: {
                    widths: ['auto', 'auto', '*'], // Defines three columns
                    body: [
                        [
                            { text: 'Score Rating', style: 'tableHeader' },
                            { text: 'Category', style: 'tableHeader' },
                            { text: 'Criteria', style: 'tableHeader' }
                        ],
                        [
                            { text: '0', style: 'tableText' },
                            { text: 'NA - Not Applicable', style: 'tableText' },
                            { text: 'Item/cause not applicable to the project/DC Ops.', style: 'tableText' }
                        ],
                        [
                            { text: '1', style: 'tableText' },
                            { text: 'Major Non-Compliance', style: 'tableText' },
                            {
                                stack: [
                                    { text: 'System failure or material reduction in the ability to provide EHS assurance due to:', style: 'tableList' },
                                    { text: 'System not in place or inadequately implemented on project/DC Ops.', style: 'tableList' },
                                    { text: 'Absence or deficiency of a required standard or practice defined in the management system.', style: 'tableList' },
                                    { text: 'Failure to address a key requirement or standard.', style: 'tableList' },
                                    { text: 'Contravention of legislative or code requirements.', style: 'tableList' },
                                ],
                                margin: [0, 0, 0, 8] // Adds spacing below the text block
                            }
                        ],
                        [
                            { text: '2', style: 'tableText' },
                            { text: 'Minor Non-Compliance', style: 'tableText' },
                            {
                                stack: [
                                    { text: 'Minimal risk of negative impact or non-compliance, which is not likely to result in a system failure or major EHS impacts, due to:', style: 'tableList' },
                                    { text: 'Some deviation from required standards or practices.', style: 'tableList' },
                                    { text: 'Partial adherence to standard/practice.', style: 'tableList' },
                                    { text: 'Single observed lapse or isolated condition.', style: 'tableList' },
                                    { text: 'Minor deviation from legislative or code requirements.', style: 'tableList' },
                                ],
                                margin: [0, 0, 0, 8]
                            }
                        ],
                        [
                            { text: '3', style: 'tableText' },
                            
                            { text: 'Compliant, with opportunity for improvement', style: 'tableText' },
                            {
                                stack: [
                                    { text: 'System is adequately in place.', style: 'tableList' },
                                    { text: 'Adequate implementation to meet required compliance standards.', style: 'tableList' },
                                    { text: 'Compliant but with suggestions for improvements to the organisation/project/DC operation.', style: 'tableList' }
                                ],
                                margin: [0, 0, 0, 8]
                            }
                        ]
                    ]
                }
            },

            {
                table: {
                    widths: ['90%', '10%'],
                    body: tableContent,
                },

                style: 'gmsTable',
            },
        ],
        footer: function (currentPage, pageCount) {

            if (currentPage !== 1) {


                return {
                    columns: [
                        {
                            text: `${data.audit.checklist.name}`,
                            width: '33.33%',
                            margin: [0, 0, 0, 0]
                        },

                        {
                            text: `${data.audit.locationOne.name} > ${data.audit.locationFour.name}`,
                            width: '33.33%',
                            alignment: 'center',
                            margin: [0, 0, 0, 0]
                        },
                        {
                            text: `Page ${currentPage} of ${pageCount}`,
                            width: '33.33%',
                            alignment: 'right',
                            margin: [0, 0, 0, 0]
                        },
                    ],
                    fontSize: 10,
                    margin: [40, 20] // Adjust margins as needed
                };
            }
        },
        defaultStyle: {
            fontSize: 10
        },
        styles: {
            tableHeader: {
                bold: true,
                fontSize: 10,
                color: 'white',
                fillColor: '#58595b', // Header background color
                alignment: 'center',
            },
            tableText: {
                fontSize: 10
            },
            tableList: {
                fontSize: 10
            },
            subHeader: {
                bold: true,
                fontSize: 10,
            },
            overallScoreLabel: {
                bold: true,
                margin: [5, 5, 5, 5],
                fontSize: 16
            },
            subHeaderWithColor: {
                bold: true,
                fontSize: 10,
                fillColor: "#58595b",
                color: 'white',
            },

            stackStyle: {
                margin: [0, 20, 0, 20]
            },
            text: {
                margin: [0, 0, 0, 5],
                fontSize: 10
            },
            stackStyle: {
                margin: [0, 20, 0, 20]
            },
            gmsHeading: {
                fontSize: 10,
                bold: true,
                // ... Other style properties
            },
            header: {
                fillColor: '#58595b', // Red background
                color: '#FFFFFF', // White text
                margin: [5, 3, 5, 3], // Adjust margins as needed
                bold: true,
                fontSize: 10,
                alignment: 'left'
            },
            tableExample: {
                margin: [0, 5, 0, 15],
                fontSize: 10
            },
            scoreLabel: {
                fontSize: 10,

            },
            scoreValue: {
                fontSize: 10,
                alignment: 'center',
                color: 'white',
                margin: [0, 5, 0, 5],
            },
            scoreValueBlack: {
                fontSize: 10,
                alignment: 'center',

                margin: [0, 5, 0, 5],
            },
            scoreCell: scoreCellStyle,
            gmsOneLabel: {

                fontSize: 10,

            },
            gmsTwoLabel: {
                margin: [5, 0, 0, 0], // Indent gmsTwo items for hierarchy
                fontSize: 10
            },
            gmsSubLabel: {
                fontSize: 10,
                bold: true,
                margin: [
                    0,
                    5,
                    0,
                    5
                ]
            },
            gmsLabel: {
                fontSize: 10,
                margin: [
                    0,
                    5,
                    0,
                    5
                ]
            },
            gmsHeading: {
                fillColor: "#58595b",
                color: 'white',
                fontSize: 10,
                bold: true,
                margin: [
                    0,
                    5,
                    0,
                    5
                ]
            },
            gmsContent: gmsContentStyle,
            gmsTable: {
                margin: [0, 5, 0, 15],
            },

            countClick: {
                alignment: 'center', // Center-align count values
            },
            defaultStyle: {
                // Define default font size and alignment
                fontSize: 10,
            },
            overallText: {
                fontSize: 10
            },
            notesStyle: {
                fontSize: 9,
                italics: true,
                color: '#666' // Light gray, as a suggestion for notes styling
            }
            // ... (other styles)
        }
    }



    return documentDefinitionData


}
