import React, { useState, useEffect } from "react";
import API from "../services/API";
import { API_URL, AUDIT_FINDINGS_URL, AUDIT_FINDINGS_WITH_ID_URL, AUDIT_GMS1_GMS2_URL, AUDIT_GMS1_URL, AUDIT_GMS2_GMS3_URL, AUDIT_GMS2_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, STATIC_URL } from "../constants";
import { Tab, Row, Col, Nav, Table, Form, Modal } from 'react-bootstrap';
import Header from "./Audit/Header";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import cogoToast from "cogo-toast";
import AuditFindings from "./AuditFindings";
import axios from "axios";
import GalleryPage from "../apps/Gallery";
import { AuditPrint } from "./AuditPrint";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import ComplianceTable from "./ComplianceTable";
import AuditSummary from "./AuditSummary";
import { secondaryPopup } from "../notifications/Swal";
import AuditFindingsEdit from "./AuditFindingsEdit";
import { Checkbox } from 'primereact/checkbox';
import { Button } from 'primereact/button';
import ActionAuditFinding from "./ActionAuditFinding";
import Swal from "sweetalert2";
import moment from 'moment'
const customSwal2 = Swal.mixin({
    customClass: {
        confirmButton: "btn btn-primary me-3",
        cancelButton: "btn btn-secondary" // Add custom class for the "No" button
    },
    buttonsStyling: false,
    showCancelButton: true, // Enable the "No" button
    confirmButtonText: 'Yes', // Text for the "Yes" button
    cancelButtonText: 'No' // Text for the "No" button
});
pdfMake.vfs = pdfFonts.pdfMake.vfs;


const AuditPanel = ({ readOnly, auditId, setAuditModal }) => {


    const [headerValues, setHeaderValues] = useState({});
    const [files, setFiles] = useState([]);
    const handleFileChange = (file) => {
        console.log(file)
        setFiles(file)

    }

    const [audit, setAudit] = useState(null)

    const [text, setText] = useState('')
    const [findings, setFindings] = useState('')

    const [gmsOne, setGmsOne] = useState([])
    const [allGmsTwo, setAllGmsTwo] = useState([])
    const [gmsTwo, setGmsTwo] = useState([])
    const [allGmsThree, setAllGmsThree] = useState([])
    const [gmsThree, setGmsThree] = useState([])

    const [selectedGmsOne, setSelectedGmsOne] = useState('')
    const [selectedGmsTwo, setSelectedGmsTwo] = useState('')
    const [selectedGmsThree, setSelectedGmsThree] = useState('')

    const [notesReport, setNotesReport] = useState({});
    const [dropdownSelections, setDropdownSelections] = useState({});
    const [auditFindingsData, setAuditFindingsData] = useState({});

    const [auditFindingModal, setAuditFindingModal] = useState(false)
    const [ncValid, setNcValid] = useState(false)

    const handleDropdownChange = (itemId, value) => {
        setDropdownSelections(prevSelections => ({
            ...prevSelections,
            [itemId]: value
        }));
    };

    const handleNotesChange = (itemId, value) => {
        setNotesReport(prevNotes => ({
            ...prevNotes,
            [itemId]: value
        }));
    };

    useEffect(() => {
        getAuditInfo();
        getAuditGmsOne();
        getAuditAllGmsTwo();
        getAuditAllGmsThree();
    }, [])
    const getAuditInfo = async () => {
        const params = {
            "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

        };
        const response = await API.get(`${AUDIT_WITH_ID_URL(auditId)}?filter=${encodeURIComponent(JSON.stringify(params))}`);



        if (response.status === 200) {
            setAudit(response.data)
            response.data?.headerInformation?.overall && setText(response.data.headerInformation.overall);
            response.data?.headerInformation?.findings && setFindings(response.data.headerInformation.findings);
            if (response.data.checklistReport)
                setDropdownSelections(response.data.checklistReport)
            if (response.data.headerInformation)
                setHeaderValues(response.data.headerInformation)
            if (response.data.notesReport)
                setNotesReport(response.data.notesReport);
        }
    }

    const getAuditAllGmsTwo = async () => {
        const response = await API.get(AUDIT_GMS2_URL);
        if (response.status === 200) {
            setAllGmsTwo(response.data)
        }
    }

    const getAuditAllGmsThree = async () => {
        const params = {
            "include": [{
                "relation": "auditFindings", "scope": {
                    "include": [{
                        "relation": "assignedTo"
                    }]
                }
            }]

        };
        const url = `${AUDIT_GMS3_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`;

        const response = await API.get(url);
        if (response.status === 200) {
            setAllGmsThree(response.data)

        }
    }

    const getAuditGmsOne = async () => {
        const response = await API.get(AUDIT_GMS1_URL);
        if (response.status === 200) {
            setGmsOne(response.data)
        }
    }

    const handleGmsOneSelect = (id) => {
        setSelectedGmsOne(id)
        setSelectedGmsTwo('')
        setSelectedGmsThree('')
    }

    useEffect(() => {
        if (selectedGmsOne) {
            getAuditGmsTwo()
        }
    }, [selectedGmsOne])

    const getAuditGmsTwo = async () => {
        const response = await API.get(AUDIT_GMS1_GMS2_URL(selectedGmsOne))
        if (response.status === 200) {
            setGmsTwo(response.data)
        }
    }

    const handleGmsTwoSelect = (id) => {

        setSelectedGmsTwo(id)
        setSelectedGmsThree('')
    }

    useEffect(() => {
        if (selectedGmsTwo) {
            getAuditGmsThree()
        }
    }, [selectedGmsTwo])

    const getAuditGmsThree = async () => {
        const params = {
            "include": [{
                "relation": "auditFindings", "scope": {
                    "include": [{
                        "relation": "assignedTo"
                    }]
                }
            }]

        };
        const url = `${AUDIT_GMS2_GMS3_URL(selectedGmsTwo)}?filter=${encodeURIComponent(JSON.stringify(params))}`;

        const response = await API.get(url)
        if (response.status === 200) {
            setGmsThree(response.data)
        }
    }

    const renderDropdown = (itemId) => (
        <Form.Control as="select" className="form-select" disabled={readOnly} value={dropdownSelections[itemId] || 0} onChange={(e) => handleDropdownChange(itemId, e.target.value)}>
            {[0, 1, 2, 3].map(num => (
                <option key={num} value={num}>{num}</option>
            ))}
        </Form.Control>
    );

    const [gmsTwoScoresAndColors, setGmsTwoScoresAndColors] = useState({}); // State to store scores and colors
    const [gmsOneScoresAndColors, setGmsOneScoresAndColors] = useState({}); // State to store scores and colors
    const [overallScoresAndColors, setOverallScoresAndColors] = useState({})
    const calculateOverallScore = () => {
        const totalSelectionsData = Object.keys(dropdownSelections).reduce((acc, key) => {
            if (dropdownSelections[key] !== '0') {
                acc[key] = dropdownSelections[key]
            }
            return acc;
        }, {});

        const totalSelections = Object.keys(totalSelectionsData).length;
        if (totalSelections === 0) {
            setOverallScoresAndColors({ score: 0, color: calculateColor(0) })
        } else {
            let sum = 0;
            Object.values(dropdownSelections).forEach(value => {
                sum += parseInt(value, 10);
            });

            const overallScore = (sum / (totalSelections * 3)) * 100;
            setOverallScoresAndColors({ score: overallScore.toFixed(2), color: calculateColor(overallScore) })
        }


    };

    useEffect(() => {
        if (Object.keys(dropdownSelections).length > 0) {
            calculateOverallScore();
        }
    }, [dropdownSelections])

    const calculateColor = (score) => {
        if (score === 0) return 'gray';
        if (score <= 59.99) return 'red';
        if (score >= 60 && score <= 79.99) return '#e29e09';
        return 'green'; // Score is 80 or above
    };

    const calculateGmsOneScore = (gmsOneId) => {
        let totalScore = 0;
        let count = 0;

        allGmsTwo.forEach(gms2 => {
            // Check if gms2 is under the current gmsOne
            if (gms2.auditGmsOneId === gmsOneId) {
                const score = calculateGmsTwoScore(gms2.id);
                if (score !== 0) { // Only consider gmsTwo with non-zero score
                    totalScore += score;
                    count++;
                }
            }
        });

        // If no gmsTwo items have been scored, return 0
        if (count === 0) {
            return 0;
        }

        return totalScore / count; // Return average score
    };

    const calculateGmsTwoScore = (gmsTwoId) => {
        let sum = 0;
        let count = 0;

        const filteredDropdownSelections = Object.keys(dropdownSelections).reduce((acc, key) => {
            if (dropdownSelections[key] !== '0') {
                acc[key] = dropdownSelections[key]
            }
            return acc;
        }, {});

        // Iterate through dropdownSelections to calculate sum and count
        Object.entries(filteredDropdownSelections).forEach(([itemId, value]) => {
            // Check if the item is under the current gmsTwoId

            if (value !== 0) {
                const isUnderGmsTwo = allGmsThree.find(item => item.id === itemId && item.auditGmsTwoId === gmsTwoId);
                if (isUnderGmsTwo) {
                    sum += parseInt(value, 10);
                    if (value !== 0) {
                        count += 1;
                    }
                }
            }

        });

        // If no items are selected (all values are 0), return gray
        if (count === 0) {
            return 0;
        }


        const score = (sum / (count * 3)) * 100; // Convert to percentage

        return score;
    };

    useEffect(() => {
        // Check if dropdownSelections is not empty and other necessary data is loaded
        if (Object.keys(dropdownSelections).length > 0 && gmsOne.length > 0 && allGmsTwo.length > 0 && allGmsThree.length > 0) {
            initiateColors();
        }
    }, [dropdownSelections, gmsOne, allGmsTwo, allGmsThree]);

    const initiateColors = () => {
        // Initialize an object to hold scores and colors for gmsOne and gmsTwo
        const initialGmsOneScoresAndColors = {};
        const initialGmsTwoScoresAndColors = {};

        // Calculate scores and colors for gmsOne
        gmsOne.forEach(gmsOneItem => {
            const gmsOneId = gmsOneItem.id;
            let totalScore = 0;
            let count = 0;

            allGmsTwo.forEach(gmsTwoItem => {
                if (gmsTwoItem.auditGmsOneId === gmsOneId) {
                    const score = calculateGmsTwoScore(gmsTwoItem.id);
                    if (score !== 0) {
                        totalScore += score;
                        count++;
                    }
                }
            });

            const gmsOneScore = count === 0 ? 0 : totalScore / count;
            initialGmsOneScoresAndColors[gmsOneId] = {
                score: gmsOneScore,
                color: calculateColor(gmsOneScore)
            };
        });

        // Calculate scores and colors for gmsTwo
        allGmsTwo.forEach(gmsTwoItem => {
            const gmsTwoId = gmsTwoItem.id;
            const score = calculateGmsTwoScore(gmsTwoId);
            initialGmsTwoScoresAndColors[gmsTwoId] = {
                score: score,
                color: calculateColor(score)
            };
        });

        // Update the state with the calculated scores and colors
        setGmsOneScoresAndColors(initialGmsOneScoresAndColors);
        setGmsTwoScoresAndColors(initialGmsTwoScoresAndColors);
    };


    useEffect(() => {

        updateColors();
    }, [dropdownSelections, selectedGmsTwo, gmsOne, allGmsTwo]);

    const updateColors = () => {

        if (selectedGmsTwo) {
            const score = calculateGmsTwoScore(selectedGmsTwo);
            const color = calculateColor(score);

            setGmsTwoScoresAndColors(prevScoresAndColors => ({
                ...prevScoresAndColors,
                [selectedGmsTwo]: { score, color }
            }));
        }


        const affectedGmsOneIds = new Set();
        if (selectedGmsTwo) {
            const affectedGmsTwo = allGmsTwo.find(gms2 => gms2.id === selectedGmsTwo);
            if (affectedGmsTwo) {
                affectedGmsOneIds.add(affectedGmsTwo.auditGmsOneId);
            }
        }


        setGmsOneScoresAndColors(prevScoresAndColors => {
            const newScoresAndColors = { ...prevScoresAndColors };
            affectedGmsOneIds.forEach(gmsOneId => {
                const score = calculateGmsOneScore(gmsOneId);
                newScoresAndColors[gmsOneId] = {
                    score: score,
                    color: calculateColor(score)
                };
            });
            return newScoresAndColors;
        });
    };

    useEffect(() => {
        // Update scores and colors for the currently selected gmsTwo

        console.log(gmsOneScoresAndColors, gmsTwoScoresAndColors)
    }, [gmsOneScoresAndColors, gmsTwoScoresAndColors]);






    const handleSave = async () => {
        // Count the selections for this gmsTwo item
        setIsLoading(true)
        if (audit) {
            const response = await API.patch(AUDIT_WITH_ID_URL(audit.id), {
                checklistReport: dropdownSelections,
                notesReport: notesReport,
                headerInformation: { ...headerValues, overall: text, findings: findings },
                status: 'Draft'
            })
            if (response.status === 204) {
                cogoToast.success('Audit Information Saved!')
                setIsLoading(false)
            } else {
                cogoToast.error('Please Try Again!')
                setIsLoading(false)
            }
        } else {
            cogoToast.error('Please Try Again!')
            setIsLoading(false)
        }
        setIsLoading(false)
    };

    const handleSubmit = async () => {
        // Count the selections for this gmsTwo item
        setIsLoading(true)
        customSwal2.fire("", "Are you sure you want to publish the report? No further changes can be made to the NCs (if any) that have been included in the report.", "warning").then(async (result) => {
            if (result.isConfirmed) {


                if (audit) {
                    const response = await API.patch(AUDIT_WITH_ID_URL(audit.id), {
                        checklistReport: dropdownSelections,
                        headerInformation: { ...headerValues, overall: text, findings: findings },
                        status: 'Submitted'
                    })
                    if (response.status === 204) {
                        cogoToast.success('Audit Information Saved!')
                        setIsLoading(false)
                        setAuditModal(false)
                    } else {
                        cogoToast.error('Please Try Again!')
                        setIsLoading(false)
                    }
                } else {
                    cogoToast.error('Please Try Again!')
                    setIsLoading(false)
                }
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                setIsLoading(false)
            }
        });
    };

    const [findingsModal, setFindingsModal] = useState(false)
    const [selectedAuditGmsThree, setSelectedAuditGmsThree] = useState('')

    const handleAuditFindings = (id) => {
        setSelectedAuditGmsThree(id)
        setFindingsModal(true)
    }

    const handleSaveInModal = async (data) => {
        //audit gms three save here
        console.log(data)
        setIsLoading(true)
        if (selectedAuditGmsThree) {


            const formData = new FormData();
            files.forEach((file, index) => {
                formData.append('file', file);
            });
            const token = localStorage.getItem('access_token');
            const fileResponse = await axios.post(`${API_URL}/files`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (fileResponse.status === 200) {

                const originalNames = fileResponse.data.files.map(file => file.originalname);
                const modifiedData = {
                    ...data,
                    uploads: originalNames,
                    auditGmsThreeId: selectedAuditGmsThree,
                    auditId: auditId
                }
                console.log(modifiedData)
                const response = await API.post(AUDIT_FINDINGS_URL, modifiedData)
                if (response.status === 200) {
                    const data = response.data;
                    setGmsThree(prev => prev.map(i => {
                        if (i.id === selectedAuditGmsThree) {
                            if (!i.auditFindings)
                                i.auditFindings = [data];
                            else
                                i.auditFindings = [...i.auditFindings, data]
                        }
                        return i;
                    }))
                    setAllGmsThree(prev => prev.map(i => {
                        if (i.id === selectedAuditGmsThree) {
                            if (!i.auditFindings)
                                i.auditFindings = [data];
                            else
                                i.auditFindings = [...i.auditFindings, data]
                        }
                        return i;
                    }))
                    cogoToast.success('Observation Saved!')
                    setIsLoading(false)
                } else {
                    cogoToast.error('Please Try Again!')
                    setIsLoading(false)
                }
            } else {
                setIsLoading(false)
            }
            getAuditGmsThree()

        }

        setIsLoading(false)
        setFindingsModal(false); // Close the modal
    };

    const handleEditInModal = async (id, data) => {
        console.log('test')
        //audit gms three save here
        setIsLoading(true)



        const formData = new FormData();
        files.forEach((file, index) => {
            formData.append('file', file);
        });
        const token = localStorage.getItem('access_token');
        const fileResponse = await axios.post(`${API_URL}/files`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            },
        });

        if (fileResponse.status === 200) {

            const originalNames = fileResponse.data.files.map(file => file.originalname);
            const modifiedData = {
                ...data,
                uploads: originalNames,

            }
            const response = await API.patch(AUDIT_FINDINGS_WITH_ID_URL(id), modifiedData)
            if (response.status === 204) {
                const data = response.data;
                getAuditAllGmsThree()
                getAuditGmsThree()
                setShowFindingListModal(false);
                cogoToast.success('Observation Saved!')
                setIsLoading(false)
            } else {
                cogoToast.error('Please Try Again!')
                setIsLoading(false)
            }
        } else {
            setIsLoading(false)
        }


        getAuditGmsThree()

        setIsLoading(false)
        setEditAuditFindingModal(false); // Close the modal
    };

    const [showFindingListModal, setShowFindingListModal] = useState(false);
    const [selectedFindingListData, setSelectedFindingListData] = useState([]);

    const handleCountClick = (data) => {

        console.log(data)

        setSelectedFindingListData(data);
        setShowFindingListModal(true);
    };

    const handleCloseModal = () => {
        setShowFindingListModal(false);
    };

    const [editAuditFindingModal, setEditAuditFindingModal] = useState(false)
    const [editAuditFindingData, setEditAuditFindingsData] = useState('')
    const handleEditAuditFindings = (data) => {
        setEditAuditFindingModal(true)
        setEditAuditFindingsData(data)
    }

    const handleDeleteAuditFindings = async (id) => {

        secondaryPopup.fire({
            title: 'Warning!',
            text: 'Are you sure?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, close it!',
            cancelButtonText: 'No, keep it'
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await API.delete(AUDIT_FINDINGS_WITH_ID_URL(id));
                if (response.status === 204) {
                    getAuditAllGmsThree()
                    getAuditGmsThree()
                    setSelectedFindingListData(prev => prev.filter(i => i.id !== id))
                    cogoToast.success('Deleted!')
                }
            }
        })

    }

    const generateTableHeaders = (category) => {
        switch (category) {
            case 'Good Practices':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Recommendations</th>
                        <th>Uploads</th>
                        {!readOnly && <th>Edit</th>}
                    </tr>
                );
            case 'Non-Conformances':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Classification</th>
                        <th>Potential Consequences</th>
                        <th>Standards & References</th>
                        <th>Recommended Mitigation Measures</th>
                        <th>Target date for closure</th>
                        <th>Action Assign Name</th>
                        <th>Uploads</th>
                        {!readOnly && <th>Edit</th>}
                    </tr>
                );
            case 'Opportunity For Improvement':
                return (
                    <tr>
                        <th>#</th>
                        <th>Findings</th>
                        <th>GMS Section</th>
                        <th>Recommendations</th>
                        <th>Standards & References</th>
                        <th>Uploads</th>
                        {!readOnly && <th>Edit</th>}
                    </tr>
                );
            default:
                return null;
        }
    };

    const generateTableRow = (item) => {
        const modifiedUploads = item.uploads ? item.uploads.map(i => {
            return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
        }) : []
        switch (item.category) {
            case 'Good Practices':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.recommendations}</td>
                        <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>
                        {!readOnly && <td><i className="mdi mdi-pencil icon-md cursor-pointer" onClick={(e) => handleEditAuditFindings(item)}></i><i className="cursor-pointer mdi mdi-delete icon-md" onClick={(e) => handleDeleteAuditFindings(item.id)} ></i></td>}
                    </tr>
                );
            case 'Non-Conformances':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.classification}</td>
                        <td>{item.potentialHazard}</td>
                        <td>{item.standardsAndReferences}</td>
                        <td>{item.recommendations}</td>
                        <td>{moment(item.dueDate, "YYYY-MM-DD").format("Do MMM YYYY")}</td>
                        <td>{item.assignedTo?.firstName}</td>
                        <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>
                        {!readOnly && <td> <i className="mdi mdi-pencil icon-md cursor-pointer" onClick={(e) => handleEditAuditFindings(item)}></i><i className="cursor-pointer mdi mdi-delete icon-md" onClick={(e) => handleDeleteAuditFindings(item.id)} ></i></td>}
                    </tr>
                );
            case 'Opportunity For Improvement':
                return (
                    <tr key={item.id}>
                        <td>{item.maskId}</td>
                        <td>{item.findings}</td>
                        <td>{item.inspectionCategories.name}</td>
                        <td>{item.recommendations}</td>
                        <td>{item.standardsAndReferences}</td>
                        <td>{item.uploads.length != 0 && <GalleryPage photos={modifiedUploads} />}</td>
                        {!readOnly && <td><i className="mdi mdi-pencil icon-md cursor-pointer" onClick={(e) => handleEditAuditFindings(item)}></i><i className="cursor-pointer mdi mdi-delete icon-md" onClick={(e) => handleDeleteAuditFindings(item.id)} ></i></td>}
                    </tr>
                );
            default:
                return null;
        }
    };

    const convertImagesToBase64 = async (array) => {
        const baseUrl = `${STATIC_URL}/`; // Replace with the actual base URL

        const processFindings = async (findings) => {
            return await Promise.all(findings.map(async (finding) => {
                if (finding.uploads && finding.uploads.length > 0) {
                    const base64Uploads = await Promise.all(
                        finding.uploads.map(async (filename) => {
                            const url = baseUrl + filename;
                            const response = await fetch(url);
                            const blob = await response.blob();
                            return new Promise((resolve, reject) => {
                                const reader = new FileReader();
                                reader.onloadend = () => resolve(reader.result);
                                reader.onerror = reject;
                                reader.readAsDataURL(blob);
                            });
                        })
                    );
                    return { ...finding, uploads: base64Uploads };
                }
                return finding;
            }));
        };

        const updatedArray = await Promise.all(
            array.map(async (item) => {
                if (item.auditFindings && item.auditFindings.length > 0) {
                    const updatedFindings = await processFindings(item.auditFindings);
                    return { ...item, auditFindings: updatedFindings };
                }
                return item;
            })
        );

        return updatedArray;
    };
    const [isLoading, setIsLoading] = useState(false)
    const handlePrint = () => {

        setIsLoading(true)
        if (audit) {

            convertImagesToBase64(allGmsThree).then(gmsThree => {
                // Do something with the updated array

                const data = {
                    audit: audit,
                    overall: text,
                    keyFindings: findings,
                    overallScoresAndColors: overallScoresAndColors,
                    allGmsOne: gmsOne,
                    allGmsTwo: allGmsTwo,
                    allGmsThree: gmsThree,
                    gmsOneScoresAndColors: gmsOneScoresAndColors,
                    gmsTwoScoresAndColors: gmsTwoScoresAndColors,
                    dropdownSelections: dropdownSelections,
                    notesReport: notesReport
                    // gmsThree: gmsThree
                }


                try {
                    AuditPrint(data).then(data => {
                        pdfMake.createPdf(data).download(`Audit_Report_${audit.checklist.name}_${audit.locationOne.name}_${audit.locationFour.name}.pdf`);
                        setIsLoading(false)
                    });

                }
                catch (e) {
                    console.log(e)
                    setIsLoading(false)
                }
            });


        }
        else {
            setIsLoading(false)
        }
    }


    const handleHeaderValuesChange = (newValues) => {
        setHeaderValues(newValues);
    };
    const Size = ReactQuill.Quill.import('attributors/style/size');
    Size.whitelist = ['10pt']; // Only allow 10pt font size
    ReactQuill.Quill.register(Size, true);

    return (
        <>

            {audit &&
                <>
                    <Header readOnly={readOnly} data={audit} onValuesChange={handleHeaderValuesChange} />
                    <br />
                    <div className="row mb-5">
                        <div className="col-6">
                            <div>Overall Assessment</div>

                            {!readOnly && <ReactQuill defaultValue={text} style={{ height: '150px', marginTop: '10px' }} onChange={(value) => setText(value)} />}
                            {readOnly && <p dangerouslySetInnerHTML={{ __html: text }}></p>}
                        </div>
                        <div className="col-6">
                            <div>Key Findings</div>
                            {!readOnly && <ReactQuill defaultValue={findings} style={{ height: '150px', marginTop: '10px' }} onChange={(value) => setFindings(value)} />}
                            {readOnly && <p dangerouslySetInnerHTML={{ __html: findings }}></p>}
                        </div>
                    </div>

                    <br />
                    <div className="row">


                        {allGmsThree && <AuditSummary gmsThree={allGmsThree} auditId={auditId} />}

                    </div>

                    <div>
                        <h5 className="my-5">Overall Score: <span style={{ color: overallScoresAndColors?.color || 'black', fontSize: '24px', fontWeight: '700' }} >{overallScoresAndColors?.score || '0'}</span></h5>
                    </div>
                    <ComplianceTable />
                    <br />
                    <Tab.Container id="left-tabs-example" className="mt-3 audit-tab" activeKey={selectedGmsOne}>
                        <Row>
                            <Col sm={12}>
                                <Nav variant="pills" className="flex-row custom-nav">
                                    {gmsOne.map(gms => (
                                        <Nav.Item key={gms.id}>
                                            <Nav.Link eventKey={gms.id} onClick={() => setSelectedGmsOne(gms.id)} style={{ borderColor: gmsOneScoresAndColors[gms.id]?.color || 'gray' }}>
                                                {gms.name}
                                                {/* <span className="color-circle" ></span> */}
                                            </Nav.Link>
                                        </Nav.Item>
                                    ))}
                                </Nav>
                            </Col>
                            <Col sm={12}>
                                <Tab.Content>
                                    {gmsOne.map(gms => (
                                        <Tab.Pane eventKey={gms.id} key={gms.id}>
                                            <h4>{gms.name}</h4>

                                            {/* Nested Tabs for gmsTwo */}
                                            <Tab.Container id={`nested-tab-${gms.id}`} activeKey={selectedGmsTwo}>
                                                <Nav variant="pills" className="nav-justified w-100 custom-nav-2">

                                                    <div className="row w-100">
                                                        {gmsTwo.map(gms2 => (
                                                            <div className="col-md-4 mb-2" key={gms2.id}>
                                                                <Nav.Link
                                                                    eventKey={gms2.id}
                                                                    onClick={() => setSelectedGmsTwo(gms2.id)}
                                                                    style={{ borderColor: gmsTwoScoresAndColors[gms2.id]?.color || 'gray' }}
                                                                    className="nav-link"
                                                                >
                                                                    <Checkbox className="custom-checkbox" checked={gms2.id === selectedGmsTwo} disabled></Checkbox>   {gms2.name}
                                                                </Nav.Link>
                                                            </div>
                                                        ))}
                                                    </div>


                                                </Nav>
                                                <Tab.Content className="p-0 border-none">
                                                    {gmsTwo.map(gms2 => (
                                                        <Tab.Pane eventKey={gms2.id} key={gms2.id}>
                                                            {/* Content for nested tab */}
                                                            <div className="table-responsive custom-table">

                                                                <table className="table-class">
                                                                    <thead>
                                                                        <tr>
                                                                            <th style={{ width: '50%' }}>Requirement</th>
                                                                            <th style={{ width: '10%' }}>Selection</th>

                                                                            <th style={{ width: '8%' }}>Good Practices</th>
                                                                            <th style={{ width: '8%' }}>OFI</th>
                                                                            <th style={{ width: '8%' }}>NC</th>
                                                                            {!readOnly && <th style={{ width: '16%' }}></th>}
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>


                                                                        {gmsThree.map(item => {


                                                                            const gp = item.auditFindings ? item.auditFindings.filter(i => i.category === 'Good Practices' && i.auditId === auditId) : [];
                                                                            const nc = item.auditFindings ? item.auditFindings.filter(i => i.category === 'Non-Conformances' && i.auditId === auditId) : [];
                                                                            const ofi = item.auditFindings ? item.auditFindings.filter(i => i.category === 'Opportunity For Improvement' && i.auditId === auditId) : [];
                                                                            console.log(nc)

                                                                            return (
                                                                                <tr key={item.id}>
                                                                                    <td className="wrapped-text">{item.name}

                                                                                        <p style={{ fontStyle: 'italic' }} contentEditable={!readOnly}
                                                                                            onBlur={(e) => handleNotesChange(item.id, e.target.innerText)}
                                                                                            suppressContentEditableWarning={true}>
                                                                                            {notesReport[item.id] || "Click to add notes..."}
                                                                                        </p>
                                                                                    </td>
                                                                                    <td>{renderDropdown(item.id)}</td>

                                                                                    <td onClick={() => handleCountClick(gp)} style={{ cursor: 'pointer' }}> <a href="#">{gp.length}</a></td>
                                                                                    <td onClick={() => handleCountClick(ofi)} style={{ cursor: 'pointer' }}><a href="#">{ofi.length} </a></td>
                                                                                    <td onClick={() => handleCountClick(nc)} style={{ cursor: 'pointer' }}><a href="#">{nc.length} </a></td>
                                                                                    {!readOnly && <td><a style={{ fontSize: '16px' }} onClick={(e) => handleAuditFindings(item.id)} className='custom-link cursor-pointer ms-2'>Record Observation</a></td>}
                                                                                </tr>
                                                                            )
                                                                        }

                                                                        )}
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </Tab.Pane>
                                                    ))}
                                                </Tab.Content>
                                            </Tab.Container>
                                        </Tab.Pane>
                                    ))}
                                </Tab.Content>
                            </Col>
                        </Row>
                    </Tab.Container>
                    <div className="d-flex justify-content-end align-items-center mt-5">
                        <Button label="Close" outlined className="me-2" onClick={() => setAuditModal(false)} >

                        </Button>
                        {!readOnly && <Button label="Save as Draft" outlined className="me-2" onClick={handleSave} >

                        </Button>}

                        {/* {!readOnly && <Button variant="primary" className="me-2" onClick={handleSubmit} >
                            Submit Report
                        </Button>} */}


                        {!readOnly && <Button variant="primary" className="me-2" onClick={handleSubmit} >
                            Publish Report And Assign Non-conformance
                        </Button>
                        }


                        {/* {!readOnly && <Button variant="primary" className="me-2" onClick={() => setAuditFindingModal(true)} >
                            Assign responsibility for addressing Non-conformance
                        </Button>} */}


                        <Button label="Download Report" outlined icon="pi pi-print" className="me-2 abs-top" onClick={() => handlePrint()}>

                        </Button>


                    </div>



                    <Modal show={showFindingListModal} size={'lg'} onHide={handleCloseModal} backdropClassName="nested-modal" centered>
                        <Modal.Header closeButton>
                            <Modal.Title>Details</Modal.Title>
                        </Modal.Header>
                        <Modal.Body>
                            <div className="table-responsive">
                                <table className="table table-bordered table-striped">
                                    <thead>
                                        {selectedFindingListData.length > 0 && generateTableHeaders(selectedFindingListData[0].category)}
                                    </thead>
                                    <tbody>

                                        {selectedFindingListData.length > 0 && selectedFindingListData.map((item) => generateTableRow(item))}

                                    </tbody>
                                </table>
                            </div>
                        </Modal.Body>
                        <Modal.Footer>
                            <button onClick={handleCloseModal} className="btn btn-light">Close</button>
                        </Modal.Footer>
                    </Modal>


                </>
            }

            {selectedAuditGmsThree &&

                <Modal
                    className="nested-modal"
                    show={findingsModal}
                    size="md"
                    onHide={() => setFindingsModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                    backdropClassName="nested-modal"
                    centered
                >


                    <Modal.Body>
                        <AuditFindings selectedAuditGmsThree={selectedAuditGmsThree} setFindingsModal={setFindingsModal} handleFileChange={handleFileChange} onSave={handleSaveInModal} auditId={auditId} />

                    </Modal.Body>

                </Modal>


            }

            {editAuditFindingModal &&

                <Modal
                    className="nested-modal"
                    show={editAuditFindingModal}
                    size="md"
                    onHide={() => setEditAuditFindingModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                    backdropClassName="nested-modal"
                    centered
                >


                    <Modal.Body>
                        <AuditFindingsEdit data={editAuditFindingData} setFindingsModal={setEditAuditFindingModal} handleFileChange={handleFileChange} onSave={handleEditInModal} auditId={auditId} />

                    </Modal.Body>

                </Modal>


            }

            {isLoading &&

                <Modal
                    className="nested-modal"
                    show={isLoading}
                    size="md"
                    onHide={() => setIsLoading(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                    backdropClassName="nested-modal"
                    centered
                >


                    <Modal.Body>

                        <h4>Please Wait!</h4>

                    </Modal.Body>

                </Modal>


            }


            <Modal
                show={auditFindingModal}
                size="lg"
                onHide={() => setAuditFindingModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <ActionAuditFinding auditId={auditId} />
                    {/* <AuditFindingPanel auditId={selectedAudit} /> */}

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setAuditFindingModal(false);
                            getAuditAllGmsThree();


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default AuditPanel;