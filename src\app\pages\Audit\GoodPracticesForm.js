import React, { useState,useEffect } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL } from "../../constants";

const GoodPracticesForm = ({ gms, aformData, handleChange, handleFileChange }) => {
    // Form fields for Good Practices
    const [file, setFile] = useState([])

    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };

    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [])


    return (
        <form>
            {/* Add your form fields here */}
            <h2>Good Practices Form</h2>
            <div className="mb-3">
                <label htmlFor="evidencePhotos" className="form-label">
                    Upload Photos
                </label>
                {file.length != 0 &&
                    <DropzoneArea
                        initialFiles={file}
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
                {file.length === 0 &&
                    <DropzoneArea
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
            </div>
            <div className="mb-3">
                <label htmlFor="inspectionCategories" className="form-label">
                    GMS Section
                </label>
                <select

                    className="form-control"
                    id="inspectionCategories"
                    name="inspectionCategories"
                    onChange={handleChange}
                    value={aformData.inspectionCategories.id}

                >
                    <option value="">Choose GMS Section</option>
                    {
                        gms.map(i => (<option key={i.id} value={i.id}>{i.name}</option>))
                    }
                </select>
            </div>

            {/* Recommendations */}
            <div className="mb-3">
                <label htmlFor="findings" className="form-label">
                    Findings
                </label>
                <textarea
                    className="form-control"
                    id="findings"
                    name="findings"
                    onChange={handleChange}
                    value={aformData.findings}
                ></textarea>
            </div>

        </form>
    );
};

export default GoodPracticesForm;