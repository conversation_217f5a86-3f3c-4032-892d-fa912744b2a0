import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import { parse, isWithinInterval, format } from "date-fns";
import moment from "moment";

const BarOne = ({ type, dateRange }) => {
    const [chartData, setChartData] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            if (type === "eptw") {
                try {
                    const response = await API.get("/permits/type-distribution-by-month");
                    const { categories, series } = response.data;

                    // Transform API response: rename types
                    const formattedSeries = series.map((item) => ({
                        ...item,
                        type: item.type === "DC" ? "Data Center" : item.type === "CA" ? "Construction Activity" : item.type
                    }));

                    // Parse dateRange into Date objects
                    const [startDate, endDate] = dateRange.map((date) => new Date(date));

                    // Filter categories based on dateRange
                    const filteredCategories = categories.filter((category) => {
                        const categoryDate = parse(category, "MMMM-yyyy", new Date());
                        return isWithinInterval(categoryDate, { start: startDate, end: endDate });
                    });

                    // Prepare series data for Highcharts
                    const seriesData = formattedSeries.map((item) => ({
                        name: item.type,
                        data: filteredCategories.map((category) => {
                            const categoryData = item.data.find((d) => d.category === category);
                            return categoryData ? categoryData.count : 0;
                        }),
                        stack: "stack1", // Stacked bars
                    }));

                    setChartData({
                        categories: filteredCategories.map((category) =>
                            moment(category, "MMM-YYYY").format("MMM YYYY") // Format as 'Jan 2024', 'Feb 2024', etc.
                        ),
                        series: seriesData,
                    });
                } catch (error) {
                    console.error("Error fetching data:", error);
                }
            }
        };

        fetchData();
    }, [type, dateRange]);

    // Highcharts Configuration
    const options = chartData
        ? {
              chart: {
                  type: "column",
                  zoomType: "xy", // 🔹 Enables zooming (drag to zoom)
              },
              title: {
                  text: "",
              },
              xAxis: {
                  categories: chartData.categories,
                  crosshair: true,
              },
              yAxis: {
                  title: {
                      text: "Number of Permits",
                  },
                  min: 0,
              },
              tooltip: {
                  shared: true,
                  valueSuffix: " permits",
              },
              plotOptions: {
                  column: {
                      stacking: "normal", // 🔹 Stacked bar chart
                      dataLabels: {
                          enabled: true,
                      },
                  },
              },
              legend: {
                  enabled: true,
              },
              series: chartData.series,
              exporting: {
                  enabled: true,
                  buttons: {
                      contextButton: {
                          menuItems: [
                              "downloadPNG",
                              "downloadJPEG",
                              "downloadPDF",
                              "downloadSVG",
                              "separator",
                              "downloadCSV",
                              "downloadXLS",
                          ],
                      },
                  },
              },
          }
        : null;

    return <>{options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}</>;
};

export default BarOne;
