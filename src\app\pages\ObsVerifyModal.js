import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Box from '@mui/material/Box';
import { API_URL, AUDIT_ACTION_URL, INSPECTION_ACTION_URL, OBSERVATION_ASSIGNEE_LIST_URL, OBSERVATION_REVIEWER_LIST_URL, OBSERVATION_REVIEWER_SUBMIT_URL, REPORT_INCIDENT_ACTIONS_WITH_ID, STATIC_URL } from "../constants";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../services/API";
import cogoToast from "cogo-toast";
import moment from 'moment';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { useSelector } from "react-redux";


const ObsVerifyModal = ({ data, applicationType, showModal, setShowModal }) => {
    const user = useSelector((state) => state.login.user);
    console.log(user)
    const more = data?.applicationDetails?.remarks != '' ? JSON.parse(data?.applicationDetails?.remarks) : {}
    const [files, setFiles] = useState([]);
    const [users, setUsers] = useState([])
    const [actionType, setActionType] = useState('approve');
    const [rejectionDate, setRejectionDate] = useState(null); // State for date picker
    const [selectedReason, setSelectedReason] = useState(''); // 
    const [actionToBeTaken, setActionToBeTaken] = useState(data.actionToBeTaken)
    const comments = useRef();
    const userId = useRef();
    const actionTaken = useRef();

    const handleFileChange = (file) => {
        setFiles(file)

    }
    useEffect(() => {
        if (data.applicationDetails.locationOneId && data.applicationDetails.locationTwoId && data.applicationDetails.locationThreeId && data.applicationDetails.locationFourId) {
            if (applicationType === 'Observation') {
                getObsUsers();

            }
        }

    }, [])

    const getObsUsers = async () => {
        const response = await API.post(OBSERVATION_ASSIGNEE_LIST_URL, { locationOneId: data.applicationDetails.locationOneId, locationTwoId: data.applicationDetails.locationTwoId, locationThreeId: data.applicationDetails.locationThreeId, locationFourId: data.applicationDetails.locationFourId });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }
    const handleSubmit = async () => {
        if (!comments.current.value) {
            cogoToast.error('Please enter your comments!')
            return;
        }

        let url = '';


        switch (applicationType) {
            case 'Observation':
                url = OBSERVATION_REVIEWER_SUBMIT_URL(data.id);
                break;
        }


        var moreDetails = Object.assign({}, more)
        let reviewers = Object.assign({}, moreDetails?.reviewers || {})
        let obj = {}
        obj['name'] = user?.firstName
        obj['id'] = user?.id
        obj['date'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
        obj['type'] = actionType == 'approve' ? 'Approved' : 'Rejected'
        obj['comments'] = comments.current.value
        reviewers[data.id] = obj
        moreDetails['reviewers'] = reviewers

        var reqData = {}
        reqData['actionType'] = actionType
        reqData['comments'] = comments.current.value

        if (actionType== 'reject') {
            let actionOwners = Object.assign([], moreDetails?.actionOwners || [])
            let obj2 = {}
            obj2['actionToBeTaken'] = actionToBeTaken

            const selectedUserId = userId.current.value;
            const selectedUser = users.find(u => u.id === selectedUserId);

            // Set 'name' and 'id' for 'obj2'
            obj2['name'] = selectedUser ? selectedUser.firstName : '';

            obj2['dueDate'] =moment(rejectionDate).format('DD-MM-YYYY')
            obj2['id'] = userId.current.value
            actionOwners.push(obj2)
            moreDetails['actionOwners'] = actionOwners

            reqData['assignedToId'] = userId.current.value
            reqData['actionToBeTaken'] = actionToBeTaken
            reqData['dueDate'] = moment(rejectionDate).format('DD-MM-YYYY')
        }
        reqData['objectId'] = data.objectId
        reqData['remarks'] = JSON.stringify(moreDetails)

        console.log(reqData)

        // const response = await API.patch(url, reqData)

        // if (response.status === 204) {
        //     cogoToast.success(actionType==='approve'?'This action has been approved!':'This action has been returned to the action assignee.')
        //     setShowModal(false)
        // }
    }


    return (
        <>
            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                backdrop="static"
            >
                <Modal.Header>
                    <div className='d-flex justify-content-between'>

                        Verify Implementation -
                        {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>}
                        {applicationType === 'INCIDENT' && `Incident - ${data.applicationDetails.title}`}
                        {applicationType === 'AuditFinding' && 'AuditFinding'}

                    </div>

                </Modal.Header>

                <Modal.Body>

                    <Box>
                        {data.applicationDetails && <div className="container">
                            <div className="card">

                                <div className="card-body">
                                    <h5 className="card-title">ID :{data.applicationDetails.maskId}</h5>
                                    {/* {applicationType === 'Observation' && <p className="card-text">{data.applicationDetails.category} Observation - <span className="text-danger">[{data.applicationDetails.type}]</span></p>} */}

                                    {!applicationType === 'Audit' && <div className="mb-3">
                                        <label className="form-label">Location</label>
                                        <input type="text" className="form-control" value={`${data.applicationDetails.locationOne && data.applicationDetails.locationOne.name} > ${data.applicationDetails.locationTwo && data.applicationDetails.locationTwo.name} > ${data.applicationDetails.locationThree && data.applicationDetails.locationThree.name} > ${data.applicationDetails.locationFour && data.applicationDetails.locationFour.name}`} readOnly />
                                    </div>}



                                    {
                                        (data.applicationDetails.uploads && data.applicationDetails.uploads.length > 0) && (
                                            <div className="mb-3">
                                                <label className="form-label">Uploads</label>
                                                <div className="border p-3 row">
                                                    {data.applicationDetails.uploads.map(i => {
                                                        const fileExtension = i.split('.').pop().toLowerCase();

                                                        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                            // Handle image files
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                                </div>
                                                            );
                                                        } else if (fileExtension === 'pdf') {
                                                            // Handle PDF files (as URLs)
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                        View PDF
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                            // Handle Excel files
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                        Download Excel File
                                                                    </a>
                                                                </div>
                                                            );
                                                        } else {
                                                            // Handle other file types or show a default message
                                                            return (
                                                                <div className="col-md-3" key={i}>
                                                                    <p>Unsupported file type: {fileExtension}</p>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )
                                    }



                                    {
                                        (!applicationType === 'INCIDENT' && data.description) && <div className="mb-3">
                                            <label className="form-label">Description</label>
                                            <textarea className="form-control" rows="3" readOnly>{data.description}</textarea>
                                        </div>
                                    }

                                    {
                                        applicationType === 'AuditFinding' ? <></> : <>
                                        </>
                                    }

                                    <div className="mb-3">
                                        <label className="form-label">Action taken</label>
                                        <textarea value={data.actionTaken} className="form-control" rows="3" readOnly></textarea>
                                    </div>
                                    {
                                        (data.uploads && data.uploads.length > 0) && (
                                            <div className="mb-3">
                                                <label className="form-label">Evidence</label>
                                                <div className="border p-3 row">
                                                    {
                                                        data.uploads.map(i => {
                                                            const fileExtension = i.split('.').pop().toLowerCase();

                                                            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                                                                // Handle image files
                                                                return (
                                                                    <div className="col-md-3" key={i}>
                                                                        <img src={`${STATIC_URL}/${i}`} alt="Uploaded content" className="img-fluid" />
                                                                    </div>
                                                                );
                                                            } else if (fileExtension === 'pdf') {
                                                                // Handle PDF files (as URLs)
                                                                return (
                                                                    <div className="col-md-3" key={i}>
                                                                        <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                            View PDF
                                                                        </a>
                                                                    </div>
                                                                );
                                                            } else if (['xls', 'xlsx'].includes(fileExtension)) {
                                                                // Handle Excel files
                                                                return (
                                                                    <div className="col-md-3" key={i}>
                                                                        <a href={`${STATIC_URL}/${i}`} target="_blank" rel="noopener noreferrer">
                                                                            Download Excel File
                                                                        </a>
                                                                    </div>
                                                                );
                                                            } else {
                                                                // Handle other file types or show a default message
                                                                return (
                                                                    <div className="col-md-3" key={i}>
                                                                        <p>Unsupported file type: {fileExtension}</p>
                                                                    </div>
                                                                );
                                                            }
                                                        })
                                                    }
                                                </div>
                                            </div>
                                        )
                                    }
                                    <div className="d-flex mb-3">
                                        <Button
                                            variant={actionType === 'approve' ? 'primary' : 'outline-primary'}
                                            className='me-2'
                                            onClick={() => setActionType('approve')}
                                        >
                                            Approve
                                        </Button>
                                        <Button
                                            variant={actionType === 'reject' ? 'primary' : 'outline-primary'}
                                            onClick={() => setActionType('reject')}
                                        >
                                            Reject/Edit
                                        </Button>
                                    </div>


                                    {data.actionToBeTaken && <div className="mb-3">
                                        <label className="form-label">Actions to be taken</label>
                                        {actionType ==='approve' ?
                                        <textarea className="form-control" rows="3" value={actionToBeTaken} onChange={(e) => setActionToBeTaken(e.target.value)} readOnly></textarea>
                                        :
                                        <textarea className="form-control" rows="3" value={actionToBeTaken} onChange={(e) => setActionToBeTaken(e.target.value)} ></textarea>
                                        }
                                    </div>}

                                    {actionType === 'reject' && (
                                        <>
                                            <div className="mb-3">
                                                <label className="form-label">Submit to</label>
                                                <select className="form-select" ref={userId}>
                                                    <option>Select</option>
                                                    {
                                                        users.map(u => (
                                                            <option key={u.id} value={u.id}>{u.firstName}</option>
                                                        ))
                                                    }
                                                </select>
                                            </div>
                                            <div className="mb-3">
                                                <label className="form-label">Due Date</label>
                                                <DatePicker
                                                    selected={rejectionDate}
                                                    onChange={(date) => setRejectionDate(date)}
                                                    minDate={new Date()}
                                                    className="form-control"
                                                    dateFormat="dd-MM-yyyy"
                                                    placeholderText="Select a date"
                                                />
                                            </div>
                                        </>
                                    )}

                                    <div className="mb-3">
                                        <label className="form-label"> Comments</label>
                                        <textarea ref={comments} className="form-control" rows="3" required></textarea>
                                    </div>


                                </div>
                            </div>
                        </div>}
                    </Box>


                </Modal.Body>

                <Modal.Footer className="flex-wrap">


                    <Button
                        variant="success"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit()}
                        sx={{ mt: 1, mr: 1 }}
                    >
                       Submit
                    </Button>
                    {/* <Button
                        variant="primary"
                        className='me-2 mt-2'
                        onClick={() => handleSubmit('reject')}
                        sx={{ mt: 1, mr: 1 }}
                    >
                        Return
                    </Button> */}

                    <Button
                        variant="light"
                        onClick={() => setShowModal(false)}
                    >
                        Close
                    </Button>




                </Modal.Footer>
            </Modal>
        </>
    )
}

export default ObsVerifyModal;