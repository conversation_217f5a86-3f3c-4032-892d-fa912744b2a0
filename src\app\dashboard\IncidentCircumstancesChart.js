import React, { useState, useEffect } from "react";
import HighchartsWrapper from "./HighchartsWrapper";
import API from "../services/API";
import moment from "moment";
import { ALL_INCIDENT_URL } from "../constants";
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';

const IncidentCircumstancesChart = ({ dateRange, filterCriteria, category }) => {
    const impactLevels = [
  'Near-Miss',
  'Level 1 (First Aid Incident - FAI)',
  'Level 2 (Medical Treatment Incident - MTI)',
  'Level 3 (Lost Time Incident - LTI)',
  'Level 4 (High Severity Incident)',
  'Level 5 (Critical Incident)'
];
    const [chartData, setChartData] = useState(null);
    const [selectedImpacts, setSelectedImpacts] = useState(impactLevels);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const params = {
                    include: [
                        { relation: "locationOne" },
                        { relation: "locationThree" },
                        { relation: "incidentCircumstanceCategory" },
                        { relation: "incidentCircumstanceType" },
                    ],
                };

                const response = await API.get(
                    `${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`
                );
                let incidents = response.data;



                incidents = incidents.filter(item => {
                    // Extract country from locationOne.name (text inside parentheses)
                    const country = item.locationOne?.name.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                    // Determine BU from locationThree.name
                    let bu = "Other";
                    const buName = item.locationThree?.name || "";

                    if (/Construction|Fitouts/i.test(buName)) {
                        bu = "Construction";
                    } else if (/DC|Data Center|Data Centre/i.test(buName)) {
                        bu = "DC";
                    } else if (/Office/i.test(buName)) {
                        bu = "Office";
                    }

                    // Normalize impact level
                    const impactRaw = (item.actualImpact || '').toLowerCase().trim();
                    let impactLevel = null;
                    if (impactRaw.includes("near miss")) {
                        impactLevel = "Near-Miss";
                    } else if (impactRaw.includes("level 1")) {
                        impactLevel = "Level 1 (First Aid Incident - FAI)";
                    } else if (impactRaw.includes("level 2")) {
                        impactLevel = "Level 2 (Medical Treatment Incident - MTI)";
                    } else if (impactRaw.includes("level 3")) {
                        impactLevel = "Level 3 (Lost Time Incident - LTI)";
                    } else if (impactRaw.includes("level 4")) {
                        impactLevel = "Level 4 (High Severity Incident)";
                    } else if (impactRaw.includes("level 5")) {
                        impactLevel = "Level 5 (Critical Incident)";
                    }





                    // Now apply the filters
                    const isCountrySelected = filterCriteria.countries.some(c => c.id === country);
                    const isBUSelected = filterCriteria.buLevels.some(b => b.id === bu);
                    const isSiteSelected = item.site ? filterCriteria.sites.some(s => s.id === item.site) : true;
                    const isImpactSelected = selectedImpacts.length === 0 || 
                        (impactLevel && selectedImpacts.includes(impactLevel));

                    return isCountrySelected && isBUSelected && isSiteSelected && isImpactSelected;
                });

                const [startDate, endDate] = dateRange.map(date => moment(date));

                // Step 1: Filter by date range using incidentDate
                const filteredIncidents = incidents.filter((incident) => {
                    const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A", true);
                    return (
                        incidentMoment.isValid() &&
                        incidentMoment.isBetween(startDate, endDate, null, "[]")
                    );
                });

                const countsByMonth = {};

                // Step 2: Group counts by monthYear → category → type
                filteredIncidents.forEach((incident) => {
                    const incidentMoment = moment(incident.incidentDate, "DD/MM/YYYY hh:mm A");
                    const monthYear = incidentMoment.format("YYYY-MM");

                    const cat = incident.incidentCircumstanceCategory?.name || "Unknown Category";
                    const type = incident.incidentCircumstanceType?.name || "Unknown Type";

                    if (!["Environment", "Health", "Safety"].includes(cat)) return;

                    if (!countsByMonth[monthYear]) {
                        countsByMonth[monthYear] = {
                            Environment: {},
                            Health: {},
                            Safety: {},
                        };
                    }

                    if (!countsByMonth[monthYear][cat][type]) {
                        countsByMonth[monthYear][cat][type] = 0;
                    }

                    countsByMonth[monthYear][cat][type] += 1;
                });

                // Generate all months in dateRange
                const allMonths = [];
                let current = startDate.clone().startOf("month");

                while (current.isSameOrBefore(endDate, "month")) {
                    allMonths.push(current.format("YYYY-MM"));
                    current.add(1, "month");
                }

                // Ensure each month has at least empty structure for the selected category
                allMonths.forEach(monthYear => {
                    if (!countsByMonth[monthYear]) {
                        countsByMonth[monthYear] = {
                            Environment: {},
                            Health: {},
                            Safety: {},
                        };
                    }

                    if (!countsByMonth[monthYear][category]) {
                        countsByMonth[monthYear][category] = {};
                    }
                });

                // Step 3: Format data by selected category
                const formattedData = allMonths.map((monthYear) => {
                    const types = countsByMonth[monthYear][category];
                    const typeList = Object.entries(types)
                        .sort((a, b) => b[1] - a[1])
                        .map(([type, count]) => ({ type, count }));

                    return {
                        monthYear,
                        types: typeList,
                    };
                });

                const labels = allMonths.map(monthYear =>
                    moment(monthYear, "YYYY-MM").format("MMM YYYY")
                );


                // Step 4: Get all unique incident types in the selected category
                const allTypes = [
                    ...new Set(formattedData.flatMap(item => item.types.map(t => t.type))),
                ];

                const customColors = [
                    "#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#E91E63",
                    "#795548", "#00BCD4", "#FFEB3B", "#FF5722", "#607D8B",
                    "#673AB7", "#3F51B5", "#8BC34A", "#CDDC39", "#F44336",
                    "#03A9F4", "#FFB300", "#D32F2F", "#388E3C", "#7B1FA2",
                ];

                // Step 5: Create series for Highcharts
                const seriesData = allTypes.map((type, index) => {
                    const dataPoints = formattedData.map((monthData) => {
                        const typeEntry = monthData.types.find(t => t.type === type);
                        return typeEntry ? typeEntry.count : 0;
                    });

                    return {
                        name: type,
                        data: dataPoints,
                        color: customColors[index % customColors.length],
                        stack: "incidentStack",
                    };
                });

                setChartData({ labels, series: seriesData });
            } catch (error) {
                console.error("Error fetching incident circumstance data:", error);
            }
        };

        fetchData();
    }, [dateRange, category, selectedImpacts]);

    const options = chartData
        ? {
            chart: {
                type: "column",
                zoomType: "xy",
            },
            title: { text: "" },
            xAxis: {
                categories: chartData.labels,
                title: { text: "Month-Year" },
                crosshair: true,
            },
            yAxis: {
                min: 0,
                title: { text: "Incident Count" },
                stackLabels: {
                    enabled: true,
                },
            },
            tooltip: {
                shared: true,
                pointFormat: "<b>{series.name}</b>: {point.y}<br/>",
            },
            plotOptions: {
                column: {
                    stacking: "normal",
                    dataLabels: {
                        enabled: true,
                    },
                },
            },
            legend: {
                layout: "vertical",
                align: "right",
                verticalAlign: "middle",
                itemMarginBottom: 5,
                labelFormatter: function () {
                    return `<span>${this.name}</span>`;
                },
                navigation: {
                    enabled: true,
                },
            },
            series: chartData.series,
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            "downloadPNG",
                            "downloadJPEG",
                            "downloadPDF",
                            "downloadSVG",
                            "separator",
                            "downloadCSV",
                            "downloadXLS",
                        ],
                    },
                },
            },
        }
        : null;

    const handleImpactChange = (value) => {
        setSelectedImpacts(value);
    };

    return (
        <div>
            <div style={{ marginBottom: '20px' }}>
                <MultiSelect
                    value={selectedImpacts}
                    options={impactLevels}
                    onChange={(e) => handleImpactChange(e.value)}
                    placeholder="Select Impact Levels"
                    style={{ width: '50%' }}
                    display="chip"
                />
            </div>
            {options ? <HighchartsWrapper options={options} /> : <p>Loading...</p>}
        </div>
    );
};

export default IncidentCircumstancesChart;
