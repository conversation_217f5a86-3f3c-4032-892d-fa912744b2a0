import React, { useState, useEffect, useRef } from "react";
import { Container, Row, Col, Card, Form, InputGroup, Modal, Button, Image } from "react-bootstrap";
import "react-datepicker/dist/react-datepicker.css";
import useForceUpdate from "use-force-update";
import { DYNAMIC_TITLES_URL, LOCATION1_URL, LOCATION_TWO, LOCATION_THREE, PERMIT_REPORT, LOCATION_FOUR, LOCATION_FIVE, DCSO_HIGN_RISK_ASSESSOR, LOCATION_SIX, ACTION_ASSIGNEE_LIST_OBS, OBSERVATION_ONE, OBSERVATION_TWO, WORK_ACTIVITIES, OBS_REPORTS, FILE_URL, EPTW_CHECKLIST } from '../../constants'
// import { Tooltip } from 'react-tooltip';
// import 'react-tooltip/dist/react-tooltip.css'
import API from "../../services/API";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import SelectCardComponent from "./component/SelectCardComponent";
import Swal from "sweetalert2";

import YesNo from "./component/YesNo";
import SignatureCanvas from 'react-signature-canvas'
import { useSelector } from "react-redux";
import moment from "moment";
import axios from "axios";
import FullLoader from "./component/FullLoader";
import {
    addHours,
    isSameDay,
    setHours,
    setMinutes,
    getHours,
    getMinutes,
    isAfter,
    isEqual,
    isBefore,
} from 'date-fns';

const Construction = ({ show, handleClose }) => {

    const user = useSelector((state) => state.login.user);
    const sign = useRef()
    // const permitChecklist = require('./component/sttPtwData.json')
    const [permitChecklist, setPermitChecklist] = useState([])
    const [highriskAssessor, setHighRiskAssesser] = useState([])
    const [highriskAssessorValue, setHighRiskAssesserValue] = useState('')
    const [title, setTitle] = useState([])
    const [locationOne, setLocationOne] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [locationFive, setLocationFive] = useState([])
    const [locationSix, setLocationSix] = useState([])
    const [locationSearchOne, setLocationSearchOne] = useState([])
    const [locationSearchTwo, setLocationSearchTwo] = useState([])
    const [locationSearchThree, setLocationSearchThree] = useState([])
    const [locationSearchFour, setLocationSearchFour] = useState([])
    const [locationSearchFive, setLocationSearchFive] = useState([])
    const [locationSearchSix, setLocationSearchSix] = useState([])
    const [checklist, setChecklist] = useState({})
    const [startDate, setStartDate] = useState(null)
    const [endDate, setEndDate] = useState(null)
    const [isWah, setisWah] = useState('')
    const [isConfined, setisConfined] = useState('')
    const [isLifting, setisLifting] = useState('')
    const [signs, setSign] = useState('')
    const [permit, setPermit] = useState([{ id: 1, label: 'Work at Height', checked: '' }, { id: 2, label: 'Confined Space', checked: '' }, { id: 3, label: 'Energised System', checked: '' }, { id: 4, label: 'Hot Works', checked: '' },
    { id: 5, label: 'Lifting Operation', checked: '' }, { id: 6, label: 'Use of Suspended PVAE', checked: '' }, { id: 7, label: 'Demolition', checked: '' }, { id: 8, label: 'Ground Disturbance', checked: '' },
    { id: 9, label: 'Hoist/Mast Climber', checked: '' }, { id: 10, label: 'Penetrations, Shafts, Risers & Voids', checked: '' }, { id: 11, label: 'Piling Works', checked: '' }],)
    const [locOne, setLocOne] = useState('')
    const [locTwo, setLocTwo] = useState('')
    const [locThree, setLocThree] = useState('')
    const [locFour, setLocFour] = useState('')
    const [locFive, setLocFive] = useState('')
    const [locSix, setLocSix] = useState('')
    const [workDescription, setWorkDescription] = useState('')
    const [searchModal, setSearchModal] = useState(false)
    const [listbk, setListBK] = useState([{ name: 'India' }, { name: 'Singapore' }, { name: 'Srilanka' }])
    const [list, setList] = useState([{ name: 'India' }, { name: 'Singapore' }, { name: 'Srilanka' }])
    const [requiredCheck, setRequiredCheck] = useState(true)
    const [loader, setLoader] = useState(false)
    const forceUpdate = useForceUpdate()


    const now = new Date();
    const maxStartDate = addHours(now, 24);

    function getStartDateMinTime() {
        if (isSameDay(now, startDate || now)) {
            return now;
        } else {
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getStartDateMaxTime() {
        if (isSameDay(maxStartDate, startDate || now)) {
            return maxStartDate;
        } else {
            return setHours(setMinutes(new Date(), 59), 23);
        }
    }

    function getEndDateMinTime() {
        if (!startDate) return setHours(setMinutes(new Date(), 0), 0);
        const start = startDate;
        const end = endDate || startDate;
        if (isSameDay(start, end)) {
            return start;
        } else {
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getEndDateMaxTime() {
        if (!startDate) return setHours(setMinutes(new Date(), 59), 23);
        const maxDateTime = addHours(startDate, 12);
        const end = endDate || startDate;
        if (isSameDay(maxDateTime, end)) {
            return maxDateTime;
        } else {
            return setHours(setMinutes(new Date(), getMinutes(maxDateTime)), getHours(maxDateTime));
        }
    }

    useEffect(() => {

        getTitle();
        getLocationOne();
        getPermitChecklist()


    }, [])

    const getTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        if (response.status === 200) {
            setTitle(response.data)
        }

    }

    const getPermitChecklist = async () => {
        const response = await API.get(EPTW_CHECKLIST);
        if (response.status === 200) {
            setPermitChecklist(response.data)
        }
    }

    const getLocationOne = async () => {
        const response = await API.get(LOCATION1_URL);
        if (response.status === 200) {
            setLocationOne(response.data)
            setLocationSearchOne(response.data)
        }

    }
    const checkPointsRemarksAction = (id1, id2, text) => {
        var obj = checklist
        obj[id1][id2].remarks = text
        setChecklist(obj)
    }
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };

    const checkPointsUserAction = (id1, id2, text) => {
        var obj = checklist
        obj[id1][id2].personnel = text
        setChecklist(obj)
    }
    const checkPointsAction = (id1, id2, i) => {
        // let index = index1+''+1
        var obj = checklist
        const checkpointObj1 = [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }]
        console.log('obj1', id2, obj)
        if (obj[id1] != undefined && obj[id1][id2] != undefined) {
            obj[id1][id2].options.forEach((data, ind) => {
                if (ind == i) {
                    data.checked = 1

                } else {
                    data.checked = 0

                }
            })
        } else {
            // obj[id1][id2] = Object.assign([], checkpointObj1)
            if (!obj[id1]) {
                obj[id1] = {};
            }
            var newObj = {}
            newObj['options'] = Object.assign([], checkpointObj1)
            newObj['remarks'] = ''
            newObj['attachments'] = []
            newObj['personnel'] = ''

            obj[id1][id2] = newObj

            // obj[id1][id2] = Object.assign([], checkpointObj1);

            console.log('obj2', obj)
            obj[id1][id2].options.forEach((data, ind) => {
                if (ind == i) {
                    data.checked = 1

                } else {
                    data.checked = 0

                }
            })
            console.log('obj3', obj)
        }
        // item[`checkpoint`] = { type: 'checkpoint', label: label, values: obj[index], remarks: ''}
        setChecklist(obj)
        forceUpdate()
    }
    const setHotWorks = (value, type, index, ii) => {


        setPermit(prev => prev.map(i => { return i.id === index ? { ...i, checked: value, options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' } : i }))
        if (ii == 0) setisWah(value)
        if (ii == 1) setisConfined(value)
        if (ii == 4) setisLifting(value)





        // const c = checklist

        // let check = {options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' }
        // c.push(check)

        // setChecklist(c)


    }
    const checkRequiredFields = () => {
        let required = true

        const requiredFields = [
            locOne, locTwo, locThree, locFour, locFive, locSix
        ];


        if (requiredFields.some(field => !field.id)) {
            setRequiredCheck(true); required = true
        } else if (!workDescription || startDate == null || endDate == null || !signs) {
            console.log('1')
            setRequiredCheck(true);
            required = true
        }




        console.log('8')
        // Function to check if one of the items in checklists is checked as 1
        // const isChecklistChecked = (permitChecklistId, permitId) => {
        //     if (Object.keys(checklist).length !== 0) {

        //         const checklist1 = checklist[permitChecklistId][permitId];
        //         // return checklist?.options?.some((item) => item.checked === 1);
        //         return checklist1.options.some((item) => item.label == 'Yes' && item.checked === 1) || checklist1.options.some((x) => x.label != 'Yes' && x.checked === 1 && checklist1.remarks)
        //     } else {
        //         return true
        //     }
        // };

        // // Function to check if all applicable items are checked for the permit
        // const checkApplicable = (permit) => {
        //     return permitChecklist.filter((item) => {
        //         return item.applicable.includes(permit.id - 1) && permit.checked === 'Yes' && !isChecklistChecked(item.id, permit.id);
        //     }).length === 0;
        // };

        // // Call the checkApplicable function for each permit
        // const checkedPermits = permit.map((permit) => {
        //     return {
        //         ...permit,
        //         allApplicableItemsChecked: checkApplicable(permit),
        //     };
        // });

        // const allApplicablePermits = checkedPermits.filter(dt => dt.checked == 'Yes')
        // console.log('checkedPermits', allApplicablePermits)
        // const isAllPermitsChecked = allApplicablePermits.every(dt2 => dt2.allApplicableItemsChecked)
        // console.log('Is all checkedPermits marked', allApplicablePermits.every(dt2 => dt2.allApplicableItemsChecked))

        // if (!isAllPermitsChecked) { setRequiredCheck(true); required = true; }
        // else { setRequiredCheck(false); required = false }

        if (!highriskAssessorValue.id) {
            setRequiredCheck(true);
            required = true
        } else { setRequiredCheck(false); required = false; }



        return required
    }
    const onsubmit = async () => {

        if (permit.every(data => data.checked === '' || data.checked === 'No')) {
            setLoader(false)
            Swal.fire('Select at least one high risk activity to proceed.')
        }
        else {

            setLoader(true)

            setTimeout(() => {
                if (checkRequiredFields()) {
                    setLoader(false)
                    setTimeout(() => {
                        Swal.fire('Please fill all the required fields')
                    }, 200)
                } else {

                    submitDcOps()

                }
                console.log('required check', requiredCheck)
            }, 2000)

        }






    }

    const config = {
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    };
    const submitDcOps = async () => {
        let signUrl = ''

        try {

            const formData = new FormData();

            formData.append('file', dataURItoFile(signs, 'sign.png'));

            const token = localStorage.getItem('access_token');
            const fileResponse = await API.post(FILE_URL, formData, config)
            if (fileResponse.status === 200) {
                signUrl = fileResponse.data.files[0].originalname;

            }
        }
        catch (e) {
            console.log(e)
        }




        console.log(signUrl)


        const allApplicablePermits = permit.filter(dt => dt.checked == 'Yes')
        var highRiskData = {}
        highRiskData['selectedPermits'] = allApplicablePermits
        highRiskData['checklists'] = checklist
        highRiskData['applicantSign'] = signUrl
        highRiskData['applicantName'] = user.firstName
        highRiskData['applicantCompany'] = user.company || user.type == 'Internal' && 'STTGDC'
        highRiskData['applicantSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
        highRiskData['assessorName'] = highriskAssessorValue.title || ''

        highRiskData['location1'] = locOne.name
        highRiskData['location2'] = locTwo.name
        highRiskData['location3'] = locThree.name
        highRiskData['location4'] = locFour.name
        highRiskData['location5'] = locFive.name
        highRiskData['location6'] = locSix.name

        var reqData = {}
        reqData['description'] = workDescription
        reqData['uploads'] = []
        reqData["created"] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
        reqData['locationOneId'] = locOne.id
        reqData['locationTwoId'] = locTwo.id
        reqData['locationThreeId'] = locThree.id
        reqData['locationFourId'] = locFour.id
        reqData['locationFiveId'] = locFive.id
        reqData['locationSixId'] = locSix.id
        reqData['permitStartDate'] = moment(startDate).format('DD-MM-YYYY hh:mm A')
        reqData['permitEndDate'] = moment(endDate).format('DD-MM-YYYY hh:mm A')
        reqData['permitType'] = 'CA'
        reqData['assessorId'] = highriskAssessorValue.id
        reqData['high_risk'] = JSON.stringify(highRiskData)
        // reqData['dcop'] = ""

        console.log(reqData)
        const response = await API.post(PERMIT_REPORT,
            JSON.stringify(reqData)
        );
        if (response.status === 200) {
            setLoader(false)
            Swal.fire({
                title: '',
                text: "This Permit has been submitted.",
                icon: 'success',

            }).then((result) => {
                if (result.isConfirmed) {
                    // navigate(-1)
                    handleClose(false)

                }
            })
        } else {
            setLoader(false)
            Swal.fire({
                title: '',
                text: "Please Try Again.",
                icon: 'warning',

            }).then((result) => {
                if (result.isConfirmed) {
                    // navigate(-1)
                    handleClose(false)

                }
            })
        }
        setLoader(false)

    }

    const selectCardComponent = (mandatory, title) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <Container onClick={() => { setSearchModal(true) }} fluid className="p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#00000060' }} > Select</Container>
                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>
        )
    }
    const selectDDCardComponent = (mandatory, title, options) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <Form.Select aria-label="Default select example">

                                    {options.map((i) => {
                                        return (
                                            <option value={i.id}>{i.name}</option>
                                        )
                                    })}

                                </Form.Select>
                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>
        )
    }
    const textAreaCardComponent = (mandatory, title) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                    <Card.Body >
                        <Row >
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <label>{title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                            </Col>
                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                <InputGroup>

                                    <Form.Control style={{ height: 100 }} as="textarea" aria-label="With textarea" />
                                </InputGroup>
                            </Col>
                        </Row>



                    </Card.Body>

                </Card>
            </Col>
        )
    }

    const YesNoComponent = (mandatory, item) => {
        return (
            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                    {item.tag && <div style={{ position: 'absolute', right: 5, top: 5 }}>
                        <i className="material-icons" style={{ fontSize: 18 }} data-tooltip-id={item.title}> help</i>
                        {/* <Tooltip
                            id={item.title}
                            content={item.tag}
                            effect="solid"
                            place="top"
                            type="light"
                            events={['click']}
                            style={{
                                zIndex: 9999,


                                width: 'calc(100vw - 20px)', // Adjust the width as needed
                                textAlign: 'center',
                            }}
                        /> */}

                    </div>
                    }
                    <Card.Body >
                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                            <label>{item.title} {mandatory && <span style={{ color: '#D62828' }}>*</span>}</label>
                        </Col>
                        <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                            <Container className="d-flex flex-wrap justify-content-center">
                                <div className="m-1" >
                                    <div onClick={() => { item.selected = true; forceUpdate(); console.log(item) }} className=" d-flex p-2" style={{ border: '1px solid #00000050', textWrap: 'nowrap', position: 'relative', borderRadius: '20px', width: 80, cursor: 'pointer' }}>
                                        <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.selected ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                                        {item.selected &&
                                            <i className="material-icons" style={{ position: 'absolute', color: 'white', left: 10 }} >check</i>

                                        }
                                        <label style={{ marginLeft: '8px', cursor: 'pointer' }}>Yes</label>
                                    </div>
                                </div>
                                <div className="m-1" >
                                    <div onClick={() => { item.selected = false; forceUpdate() }} className=" d-flex p-2" style={{ border: '1px solid #00000050', textWrap: 'nowrap', position: 'relative', borderRadius: '20px', width: 80, cursor: 'pointer' }}>
                                        <Container style={{ width: 24, height: 24, borderRadius: 12, background: !item.selected ? '#D62828' : 'lightgray', cursor: 'pointer' }} />
                                        {!item.selected &&
                                            <i className="material-icons" style={{ position: 'absolute', color: 'white', left: 12 }} >check</i>

                                        }
                                        <label style={{ marginLeft: '8px', cursor: 'pointer' }}>No</label>
                                    </div>
                                </div>
                            </Container>
                        </Row>
                    </Card.Body>
                </Card>
            </Col>
        )
    }
    const getApproverAndRep = async () => {

        var reqData = {}
        reqData['locationOneId'] = locOne.id
        reqData['locationTwoId'] = locTwo.id
        reqData['locationThreeId'] = locThree.id
        reqData['locationFourId'] = locFour.id
        reqData['isWah'] = isWah == 'Yes'
        reqData['isConfinedSpace'] = isConfined == 'Yes'
        reqData['isLifting'] = isLifting == 'Yes'

        const response1 = await API.post(DCSO_HIGN_RISK_ASSESSOR, reqData);
        if (response1.status === 200) {

            var res = response1.data.map(i => ({ ...i, title: i.firstName }));

            setHighRiskAssesser(res)
        }

    }
    const updateSelected = async (item, type) => {
        if (type === 'one') {
            setLocOne(item)
            setLocTwo('')
            setLocThree('')
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_TWO(item.id));
            if (response.status === 200) {
                setLocationTwo(response.data)
                setLocationSearchTwo(response.data)

            }
        } else if (type == 'two') {
            setLocTwo(item)
            setLocThree('')
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_THREE(item.id));
            if (response.status === 200) {

                console.log(response.data)
                console.log(item)
                if (item.name === 'Singapore') {
                    const filteredData = response.data.filter(item => item.name !== "Data Centre Operations");
                    console.log(filteredData);
                    setLocationThree(filteredData);
                    setLocationSearchThree(response.data);
                } else {
                    const filteredData = response.data.filter(item => item.name === "Construction Projects");
                    console.log(filteredData);
                    setLocationThree(filteredData);
                    setLocationSearchThree(response.data);
                }

                // setLocationThree(response.data)
                // setLocationSearchThree(response.data)

            }
        }
        else if (type == 'three') {
            setLocThree(item)
            setLocFive('')
            setLocFour('')
            setLocSix('')
            const response = await API.get(LOCATION_FOUR(item.id));
            if (response.status === 200) {
                setLocationFour(response.data)
                setLocationSearchFour(response.data)

            }
        }
        else if (type == 'four') {
            setLocFour(item)
            setLocFive('')

            setLocSix('')
            const response = await API.get(LOCATION_FIVE(item.id));
            if (response.status === 200) {
                setLocationFive(response.data)
                setLocationSearchFive(response.data)

            }
        }
        else if (type == 'five') {
            setLocFive(item)
            setLocSix('')
            const response = await API.get(LOCATION_SIX(item.id));
            if (response.status === 200) {
                setLocationSix(response.data)
                setLocationSearchSix(response.data)

            }
        }
        else if (type == 'six') {
            setLocSix(item)

        }


    }
    const searchList = (e) => {
        console.log(e.target.value)
        setList(listbk.filter((i) => { return i.name.toLowerCase().includes(e.target.value.toLowerCase()) }))
    }
    const onCloseOBS = () => {
        Swal.fire({
            title: '',
            text: "Are you sure you wish to close this session? All data entered here will be reset.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes'
        }).then((result) => {
            if (result.isConfirmed) {
                // navigate(-1)
                handleClose(false)

            }
        })
    }

    const searchValue = (name, type) => {
        if (type === 'one') {
            const one = locationSearchOne
            setLocationOne(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'two') {
            const one = locationSearchTwo
            setLocationTwo(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'three') {
            const one = locationSearchThree
            setLocationThree(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'four') {
            const one = locationSearchFour
            setLocationFour(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'five') {
            const one = locationSearchFive
            setLocationFive(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'six') {
            const one = locationSearchSix
            setLocationSix(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }



    }
    return (<>
        <Modal
            show={show}
            onHide={() => { handleClose(false) }}
            size="lg"
        >
            <Modal.Header closeButton>
                <Modal.Title >
                    Construction Activity - ePTW
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>

                <Row >
                    {/* <Col className="font-bold mt-2 mb-2">
                    <text>Construction Activity - ePTW</text>
                </Col> */}
                    <Row>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[0].altTitle : ''} data={locationOne} selectedValue={locOne.id} label={locOne === '' ? 'Select' : locOne.name} updateListSelected={updateSelected} type={'one'} searchList={searchValue} box={true} />
                        </Col>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[1].altTitle : ''} data={locationTwo} selectedValue={locTwo.id} label={locTwo === '' ? 'Select' : locTwo.name} updateListSelected={updateSelected} type={'two'} searchList={searchValue} box={true} />
                        </Col>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[2].altTitle : ''} data={locationThree} selectedValue={locThree.id} label={locThree === '' ? 'Select' : locThree.name} updateListSelected={updateSelected} type={'three'} searchList={searchValue} box={true} />
                        </Col>
                    </Row>

                    <Row>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[3].altTitle : ''} data={locationFour} selectedValue={locFour.id} label={locFour === '' ? 'Select' : locFour.name} updateListSelected={updateSelected} type={'four'} searchList={searchValue} box={true} />
                        </Col>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={locFive.id} label={locFive === '' ? 'Select' : locFive.name} updateListSelected={updateSelected} type={'five'} searchList={searchValue} box={true} />
                        </Col>
                        <Col>
                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={locSix.id} label={locSix === '' ? 'Select' : locSix.name} updateListSelected={updateSelected} type={'six'} searchList={searchValue} box={true} />
                        </Col>
                    </Row>
                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                            <Card.Body >
                                <Row >
                                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                        <label>Description of work <span style={{ color: '#D62828' }}>*</span></label>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start">
                                        <InputGroup>

                                            <Form.Control style={{ height: 100 }} as="textarea" aria-label="With textarea"
                                                onChange={(e) => setWorkDescription(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Col>
                                </Row>



                            </Card.Body>

                        </Card>
                    </Col>
                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body>
                                <Row>
                                    <Col>
                                        <label>
                                            {`Work Start Date & Time`} <span style={{ color: '#D62828' }}>*</span>
                                        </label>
                                    </Col>
                                    <Col>
                                        <ReactDatePicker
                                            showIcon
                                            className="form-control"
                                            showTimeSelect
                                            timeFormat="h:mm aa"
                                            timeIntervals={15}
                                            timeCaption="time"
                                            dateFormat="dd/MM/yyyy h:mm aa"
                                            selected={startDate}
                                            onChange={(date) => {
                                                setStartDate(date);
                                                setEndDate(null); // Reset endDate when startDate changes
                                            }}
                                            minDate={now}
                                            maxDate={maxStartDate}
                                            minTime={getStartDateMinTime()}
                                            maxTime={getStartDateMaxTime()}
                                            filterTime={(time) => {
                                                const selectedDate = startDate || now;
                                                const currentTime = new Date(time);
                                                const minTime = getStartDateMinTime();
                                                const maxTime = getStartDateMaxTime();
                                                return (
                                                    (isAfter(currentTime, minTime) || isEqual(currentTime, minTime)) &&
                                                    (isBefore(currentTime, maxTime) || isEqual(currentTime, maxTime))
                                                );
                                            }}
                                            filterDate={(date) => {
                                                return (
                                                    (isAfter(date, now) || isEqual(date, now)) &&
                                                    (isBefore(date, maxStartDate) || isEqual(date, maxStartDate))
                                                );
                                            }}
                                        />
                                    </Col>
                                    <Col>
                                        <label>
                                            {`Work End Date & Time`} <span style={{ color: '#D62828' }}>*</span>
                                        </label>
                                    </Col>
                                    <Col>
                                        <ReactDatePicker
                                            showIcon
                                            className="form-control"
                                            showTimeSelect
                                            timeFormat="h:mm aa"
                                            timeIntervals={15}
                                            timeCaption="time"
                                            dateFormat="dd/MM/yyyy h:mm aa"
                                            selected={endDate}
                                            onChange={(date) => setEndDate(date)}
                                            minDate={startDate || now}
                                            maxDate={startDate ? addHours(startDate, 12) : null}
                                            minTime={
                                                startDate
                                                    ? new Date(startDate).setHours(
                                                        startDate.getHours(),
                                                        startDate.getMinutes(),
                                                        0,
                                                        0
                                                    )
                                                    : getEndDateMinTime()
                                            }
                                            maxTime={
                                                startDate
                                                    ? new Date(startDate).setHours(23, 59, 59, 999) // End time on the same date
                                                    : getEndDateMaxTime()
                                            }
                                            filterTime={(time) => {
                                                if (!startDate) return true;
                                                const currentTime = new Date(time);
                                                const minTime = new Date(startDate).setHours(
                                                    startDate.getHours(),
                                                    startDate.getMinutes(),
                                                    0,
                                                    0
                                                );
                                                const maxTime = new Date(startDate).setHours(23, 59, 59, 999);
                                                return (
                                                    (isAfter(currentTime, minTime) || isEqual(currentTime, minTime)) &&
                                                    (isBefore(currentTime, maxTime) || isEqual(currentTime, maxTime))
                                                );
                                            }}
                                            disabled={!startDate}
                                        />
                                    </Col>
                                </Row>

                            </Card.Body>
                        </Card>
                    </Col>
                    {permit.map((item, i) => {
                        return (
                            <YesNo title={`Does this work involve ${item.label}  `} value={item.checked} index={item.id} i={i} checkYesNo={setHotWorks} type={item.label} />
                        )
                    })}


                    {permit.some(data => data.checked === 'Yes') && <>

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', background: 'red', textAlign: 'center', color: '#fff', padding: 10 }}>
                                <h5>Safety Checklist</h5>
                                <p>Physical Verification of Compliance Required</p>
                            </Card>
                        </Col>

                        {permitChecklist.map((item, index) => {
                            const hasMatch = permit.some(data => item.applicable.includes((data.id - 1)) && data.checked === 'Yes');
                            if (hasMatch) {

                                return (
                                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                            <Card.Body >
                                                <Row  >
                                                    <Row>
                                                        <Col xs={12} sm={12} md={12} className="d-flex ">
                                                            <label>{index + 1}.{item.label} <span style={{ color: '#D62828' }}>*</span></label>
                                                        </Col>
                                                    </Row>


                                                </Row>

                                                {permit.map((data, i) => {
                                                    if (item.applicable.includes(i) && data.checked == 'Yes') {
                                                        var checklists = checklist[item.id] != undefined && checklist[item.id][data.id] != undefined ? checklist[item.id][data.id] : { options: [{ label: 'Yes', checked: 0 }, { label: 'No', checked: 0 }, { label: 'Not Applicable', checked: 0 }], remarks: '', attachments: [], personnel: '' }


                                                        return (<>
                                                            <Row className="p-1">
                                                                <Col xs={12} sm={12} md={12} className="d-flex ">
                                                                    <label>Confirm {data.label} Activity <span style={{ color: '#D62828' }}>*</span></label>



                                                                </Col>
                                                            </Row>
                                                            <Row className="col-12 mt-2 mb-2 m-auto p-0 " >
                                                                {checklists.options.map((dt, ind) => {

                                                                    return (
                                                                        <Col xs={4} sm={4} md={4} className="p-1" style={{ color: '#000', textAlign: 'center', border: '1px solid', background: dt.checked === 1 ? ind === 0 ? '#1bbc9b' : ind === 1 ? '#D62828' : '#ffa500' : '#fff' }} onClick={() => checkPointsAction(item.id, data.id, ind)}>
                                                                            {dt.label}
                                                                        </Col>
                                                                    )
                                                                })}



                                                            </Row>

                                                            {(checklist[item.id] != undefined && checklist[item.id][data.id] != undefined)
                                                                ? checklist[item.id][data.id].options.some(option => option.label === 'Yes' && option.checked === 1) ?
                                                                    item.attachment ? null

                                                                        : item.user &&

                                                                        <Row >
                                                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                                                                <label>{item.userText} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                                                            </Col>
                                                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                                                                <InputGroup className="mb-3">
                                                                                    <Form.Control
                                                                                        placeholder=""
                                                                                        type="text"
                                                                                        onChange={(e) => checkPointsUserAction(item.id, data.id, e.target.value)}
                                                                                    />

                                                                                </InputGroup>
                                                                            </Col>
                                                                        </Row>


                                                                    :

                                                                    <Row >
                                                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start">
                                                                            <label>{'Remarks (if any)...'} {<span style={{ color: '#D62828' }}>*</span>}</label>
                                                                        </Col>
                                                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                                                                            <InputGroup className="mb-3">
                                                                                <Form.Control
                                                                                    placeholder=""
                                                                                    type="text"
                                                                                    onChange={(e) => checkPointsRemarksAction(item.id, data.id, e.target.value)}
                                                                                />

                                                                            </InputGroup>
                                                                        </Col>
                                                                    </Row>

                                                                : null

                                                            }


                                                        </>)

                                                    }
                                                })}




                                            </Card.Body>

                                        </Card>
                                    </Col>
                                )

                            }

                        })}

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row>

                                        <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                            <label style={{ textAlign: 'justify' }}>I confirm that I am the supervisor in charge of the activity(ies) and am suitably competent to carry out the Person-In-Charge function. I have read and fully understand the SWMS/SWP and all the safety precautions to be taken under current legislations and STT GDC’s Group Minimum Standards. I have prepared the work area(s) to be safe for the task(s) and have visually confirmed all SWMS/SWP & Safety Checklist conditions are complied with. To the best of my knowledge this activity is safe to proceed.</label>
                                        </Col>

                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true); getApproverAndRep() }}>
                                            <span class="material-icons" style={{ fontSize: 60 }}>
                                                draw
                                            </span>


                                        </Col>

                                        <div className="d-flex justify-content-center">
                                            {signs !== '' &&
                                                <img src={signs} height={100} style={{ minWidth: 150 }} />
                                            }
                                        </div>

                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>
                        <SelectCardComponent mandatory={true} title={'Select High Risk Assessor'} data={highriskAssessor} selectedValue={highriskAssessorValue.id} label={highriskAssessorValue === '' ? 'Select' : highriskAssessorValue.title} updateListSelected={(data, type) => {
                            setHighRiskAssesserValue(data)
                        }} type={'owner'} searchList={searchValue} box={true} />
                    </>}


                </Row>
                <Row style={{ height: 50, borderRadius: 10, background: '#D62828', color: 'white' }} onClick={() => onsubmit()} className="align-items-center m-auto"  >
                    <label >Submit </label>
                </Row>
                {loader && <FullLoader />}
            </Modal.Body>

        </Modal>
        <Modal
            show={searchModal}
            onHide={() => { setSearchModal(false) }}
            aria-labelledby="contained-modal-title-vcenter"
            centered
            backdrop={'static'}
        >
            <Modal.Header closeButton={false}>
                <Modal.Title id="contained-modal-title-vcenter">
                    Sign
                </Modal.Title>
            </Modal.Header>
            <Modal.Body style={{ background: '#f5f5f5' }}>
                <SignatureCanvas
                    ref={sign}
                    penColor='green'
                    backgroundColor="white"
                    canvasProps={{
                        className: "sigCanvas",
                        style: {
                            width: '100%', // Ensures the canvas takes up the full width
                            background: '#fff',
                            boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            height: '100px'
                        },
                    }}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={() => { setSign(sign.current.getTrimmedCanvas().toDataURL("image/png")); setSearchModal(false) }}>confirm</Button>
                <Button onClick={() => sign.current.clear()}>Clear</Button>
                <Button onClick={() => { setSearchModal(false) }}>Close</Button>
            </Modal.Footer>
        </Modal>

    </>
    )
}

export default Construction