import React from 'react';


const ComplianceTable = () => {
    const scoreInfo = [
        {
            score: 0,
            category: "Not Applicable",
            criteria: [
                "Item/cause not applicable to the project/DC ops."
            ]
        },
        {
            score: 1,
            category: "Major Non-Compliance",
            criteria: [
                "System failure or material reduction in the ability to provide EHS assurance due to:",
                "System not in place or inadequately implemented on project/DC Ops",
                "Absence or deficiency of a required standard or practice defined in the management system",
                "Failure to address a key requirement or standard",
                "Contravention of legislative or code requirements",
               
            ]
        },
        {
            score: 2,
            category: "Minor Non-Compliance",
            criteria: [
                "Minimal risk of negative impact or non-compliance, which is not likely to result in a system failure or major EHS impacts, due to:",
                "Some deviation from required standards or practices",
                "Partial adherence to standard/practice",
                "Single observed lapse or isolated condition",
                "Minor deviation from legislative or code requirements."
            ]
        },
        {
            score: 3,
            category: "Compliant, with opportunity for improvement",
            criteria: [
                "System is adequately in place",
                "Adequate implementation to meet required compliance standards.",
                "However, there are suggestions for improvement to the Organisation/Project/DC Operation."
            ]
        }
    ];

    return (
        <div className="score-details-container">
            {scoreInfo.map((info, index) => (
                <div key={index} className="score-box">
                    <h4>Score rating: {info.score}</h4>
                    <p>Category: {info.category}</p>
                    <ul>
                        {info.criteria.map((item, idx) => <li key={idx}>{item}</li>)}
                    </ul>
                </div>
            ))}
        </div>
    );
};

export default ComplianceTable;
