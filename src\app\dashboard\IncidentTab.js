import React, { useEffect, useState } from 'react'
import 'primereact/resources/themes/saga-blue/theme.css';  //theme
import 'primereact/resources/primereact.min.css';          //core css
import 'primeicons/primeicons.css';                        //icons
import { Container, Row, Col, Card, Nav, Tab } from 'react-bootstrap';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import WaterChart from './WaterChart';
import BarOne from './BarOne';
import BarSix from './BarSix';
import API from '../services/API';
import IncidentCategoryPieChart from './IncidentCategoryPieChart';
import IncidentImpactBarChart from './IncidentImpactBarChart';
import TopCategoriesBarChart from './TopCategoriesBarChart';
import TopWorkplaceActivitiesBarChart from './TopWorkplaceActivitiesBarChart';
import TopGMSCategoriesBarChart from './TopGMSCategoriesBarChart';
import { getDisplayDateRange } from './dateUtils';
import IncidentCircumstancesChart from './IncidentCircumstancesChart';
import OverallIncidentCircumstancesChart from './OverallIncidentCircumstancesChart';
function IncidentTab({ dateRange, filterCriteria }) {
    const displayDateRange = getDisplayDateRange(dateRange);

    const [chartData, setChartData] = useState(null);

    const emissionsData = [
        { month: 'Jan', emission: 32000 },
        { month: 'Feb', emission: 16000 },
        { month: 'Mar', emission: 32000 },
        { month: 'Apr', emission: 10000 },
        { month: 'May', emission: 42000 },
        { month: 'Jun', emission: 2000 },
        // Add more months as needed
    ];

    const emissionsdueData = [
        { month: 'Jan', emission: 32 },
        { month: 'Feb', emission: 16 },
        { month: 'Mar', emission: 34 },
        { month: 'Apr', emission: 50 },
        { month: 'May', emission: 55 },
        { month: 'Jun', emission: 75 },
        // Add more months as needed
    ];

    const emissionsFData = [
        { month: 'Jan', emission: 32 },
        { month: 'Feb', emission: 80 },
        { month: 'Mar', emission: 1540 },
        { month: 'Apr', emission: 2600000 },
        { month: 'May', emission: 3750000 },
        { month: 'Jun', emission: 4850000000 },
        // Add more months as needed
    ];
    const emissionsTData = [
        { month: 'Jan', emission: 32000 },
        { month: 'Feb', emission: 16000 },
        { month: 'Mar', emission: 32000 },
        { month: 'Apr', emission: 10000 },
        { month: 'May', emission: 4200 },
        { month: 'Jun', emission: 2000 },
        // Add more months as needed
    ];

    const styles = {
        cardContainer: {
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            padding: '20px',
            height: '100%'
        },
        textContent: {
            flex: 1
        },
        emissionsValue: {
            fontSize: '18px',
            fontWeight: 'bold',
            marginBottom: '5px'
        },
        targetText: {
            color: 'black',
            fontWeight: 'normal',
            fontSize: '16px'
        },
        greenText: {
            color: 'green',
            fontWeight: 'bold'
        },
        redText: {
            color: 'red',
            fontWeight: 'bold'
        },
        chartContainer: {
            width: 150,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%'
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await API.post('/incidents/statistics');




                setChartData(response.data);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, []);
    return (<>
        <Row className="mb-4">
            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    {chartData?.highSeverityPercentage}
                                </div>
                                <div style={styles.targetText}>
                                    High severity incidents
                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="green" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    89%
                                </div>
                                <div style={styles.targetText}>
                                    Incident investigations completed within identified time

                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsdueData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="red" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    {chartData?.actionsClosedOnTimePercentage}
                                </div>
                                <div style={styles.targetText}>
                                    Actions identified post incident closed within due date

                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsTData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="green" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>

            <Col md={3}>
                <Card >
                    <Card.Body className="d-flex flex-column">
                        <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                            <div>
                                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                    {chartData?.overdueActionsPercentage}
                                </div>
                                <div style={styles.targetText}>
                                    Actions identified post incident close after dueDate
                                    {/* Root causes of investigated incidents that are repetitive */}
                                </div>
                            </div>
                            <div style={styles.chartContainer}>
                                <LineChart width={180} height={50} data={emissionsFData}>
                                    <XAxis dataKey="name" hide />
                                    <YAxis hide />
                                    <Tooltip />
                                    <Line type="monotone" dataKey="emission" stroke="red" strokeWidth={2} />
                                </LineChart>
                            </div>
                        </div>
                    </Card.Body>
                </Card>
            </Col>
        </Row>

        <Row className="mb-4">
            <Col md={6}>
                <Card >
                    <Card.Body>
                        <h5 className='font-weight-bold'>Breakdown of Incident Category | {displayDateRange}</h5>


                        <IncidentCategoryPieChart dateRange={dateRange} filterCriteria={filterCriteria} />
                    </Card.Body>
                </Card>
            </Col>
            <Col md={6}>
                <Card >
                    <Card.Body>
                        <h5 className='font-weight-bold'>Impact Classification  | {displayDateRange}</h5>

                        <IncidentImpactBarChart dateRange={dateRange} filterCriteria={filterCriteria} />
                    </Card.Body>
                </Card>
            </Col>
        </Row>
        <Row className="mb-4">
            <Col md={12}>
                <Card >
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Incident Category | {displayDateRange}</h5>


                        <TopCategoriesBarChart dateRange={dateRange} filterCriteria={filterCriteria} />
                    </Card.Body>
                </Card>
            </Col>

            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Incident Circumstances (Overall) | {displayDateRange}</h5>


                        <OverallIncidentCircumstancesChart dateRange={dateRange} filterCriteria={filterCriteria} />

                    </Card.Body>
                </Card>
            </Col>
        </Row>
        <Row className="mb-4">
            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Incident circumstance (Environment) | {displayDateRange}</h5>


                        <IncidentCircumstancesChart dateRange={dateRange} filterCriteria={filterCriteria} category="Environment" />

                    </Card.Body>
                </Card>
            </Col>


            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Incident circumstance (Health) | {displayDateRange}</h5>


                        <IncidentCircumstancesChart dateRange={dateRange} filterCriteria={filterCriteria} category="Health" />
                    </Card.Body>
                </Card>
            </Col>
        </Row>
        <Row className="mb-4">
            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Incident circumstance (Safety) | {displayDateRange}</h5>


                        <IncidentCircumstancesChart dateRange={dateRange} filterCriteria={filterCriteria} category="Safety" />
                    </Card.Body>
                </Card>
            </Col>


            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Workplace Activities | {displayDateRange}</h5>

                        <TopWorkplaceActivitiesBarChart filterCriteria={filterCriteria} dateRange={dateRange} />
                    </Card.Body>
                </Card>
            </Col>
        </Row>
        <Row className="mb-4">

            <Col md={12}>
                <Card className="mt-5">
                    <Card.Body>
                        <h5 className='font-weight-bold'>Top Gms Categories | {displayDateRange}</h5>


                        <TopGMSCategoriesBarChart filterCriteria={filterCriteria} dateRange={dateRange} />
                    </Card.Body>
                </Card>
            </Col>
            {/* <Col md={6}>
                <Card >
                    <Card.Body>
                        <h5 className='card-title'>Top Workplace Activities</h5>
                     
                       <TopWorkplaceActivitiesBarChart />
                    </Card.Body>
                </Card>
            </Col> */}
        </Row>


    </>)
}

export default IncidentTab