import React, { useEffect, useState } from "react";
import moment from 'moment';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MultiSelect } from 'primereact/multiselect';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import ReportDataModal from "../ReportDataModal";
import { Badge } from 'react-bootstrap';
import ReviewDataModal from './ReviewReportModal'
import API from "../../services/API";
import { ALL_REPORT_DATA_URL_WITH_ID } from "../../constants";
const ActionCard = ({ action, applicationType, setRendered }) => {
    console.log(action)
    const currentDate = moment();
    const [data, setData] = useState([]);
    const [requiredAction, setRequiredAction] = useState([]);
    const [reportModal, setReportModal] = useState(false);
    const [actionOne, setActionOne] = useState({});
    const [actionOneLast, setActionOneLast] = useState({});
    const [reviewModal, setReviewModal] = useState(false)
    const [reviewModalData, setReviewModalData] = useState([])
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: 'contains' },
        dueStatus: { value: null, matchMode: 'in' }
    });

    const checkDueStatus = (report) => {
        const [month, year] = report.dueDate.split(' '); // Split the due date string into month and year
        const dueDate = new Date(`${month} 15, ${year}`); // Create a Date object with the 15th of the due month

        // Calculate the date for the same day in the next month
        const dueDateNew = new Date(dueDate);
        const nextMonthDate = new Date(dueDateNew.setMonth(dueDateNew.getMonth() + 1));
        const formattedDate = nextMonthDate.toDateString();

        const currentDate = new Date(); // Get the current date

        // Compare the current date with the formatted date of the next month
        if (currentDate.toDateString() === formattedDate) {
            return 'Due Soon';
        } else if (currentDate > nextMonthDate) {
            return 'Overdue';
        } else {
            return 'Due Soon';
        }
    };


    useEffect(() => {
        const final = action.map((item) => {
            item.dueStatus = checkDueStatus(item);
            return item;
        });
        setData(final);
        setRequiredAction(final.map(item => item.dueStatus).filter((value, index, self) => self.indexOf(value) === index));
    }, [action]);

    const renderHeader = () => {
        return (
            <div className='d-flex justify-content-between align-items-center'>
                <h5 className="m-0">A listing of all actions due from you for the selected location(s) and time frame.</h5>
            </div>
        );
    };

    const header = renderHeader();

    const dueStatusFilterTemplate = (options) => {
        return (
            <MultiSelect value={options.value} options={requiredAction.map(status => ({ label: status, value: status }))} onChange={(e) => options.filterApplyCallback(e.value)} optionLabel="label" placeholder="Any" className="p-column-filter" />
        );
    };

    const maskIdBodyTemplateForReport = (row) => {
        return <div className='maskid' onClick={() => handleAction(row)}>  # {row.dueDate} - {projectFirstReport(row)} </div>;
    }

    const dueStatusBosyTemplate = (row) => {
        if (row.dueStatus === 'Overdue') {
            return <Badge pill bg={'danger'} >{row.dueStatus}</Badge>
        } else {
            return <Badge pill bg={'warning'} >{row.dueStatus}</Badge>
        }
    }

    const requiredBodyAction = (row) => {
        if (row.actionType === "report_review") {
            return <>{'Review Monthly Report ( ' + row.dueDate + ' )'}</>
        }
        return <>{'Submit Monthly Report ( ' + row.dueDate + ' )'}</>;
    }

    const dueBodyTemplate = (row) => {
        return (
            <div> {'15th ' + moment(row.dueDate, "MMM YYYY").add(1, 'month').format('MMM YYYY')}</div>
        );
    }

    const handleAction = async (action) => {
        setActionOne(action);
        if (action.actionType === 'report') {
            setReportModal(true);
        } else if (action.actionType === 'report_review') {
            const response = await API.get(ALL_REPORT_DATA_URL_WITH_ID(action.objectId))
            if (response.status === 200) {
                setActionOneLast(response.data)
            }
            setReviewModal(true)
        }

    };

    const projectFirstReport = (row) => {
        if (!row || !row.applicationDetails) {
            return '';
        }
        const details = row.applicationDetails || {};
        const locationOne = details.locationOne && details.locationOne.name ? details.locationOne.name.charAt(0) + ' > ' : '';
        const locationTwo = details.locationTwo && details.locationTwo.name ? details.locationTwo.name.charAt(0) + ' > ' : '';
        const locationThree = details.locationThree && details.locationThree.name ? details.locationThree.name.charAt(0) + ' > ' : '';
        const locationFour = details.locationFour && details.locationFour.name ? details.locationFour.name.charAt(0) : '';
        const fullLocationChain = locationOne + locationTwo + locationThree + locationFour;

        return fullLocationChain || '';
    }

    const projectBodyTemplateForReport = (row) => {
        const details = row.applicationDetails || {};
        const locationOne = details.locationOne ? details.locationOne.name + ' > ' : '';
        const locationTwo = details.locationTwo ? details.locationTwo.name + ' > ' : '';
        const locationThree = details.locationThree ? details.locationThree.name + ' > ' : '';
        const locationFour = details.locationFour ? details.locationFour.name : '';

        const fullLocationChain = locationOne + '' + locationTwo + '' + locationThree + '' + locationFour;

        return fullLocationChain || '';
    }

    return (
        <>
            <DataTable value={data} paginator rows={10} header={header} filters={filters} onFilter={(e) => setFilters(e.filters)} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                rowsPerPageOptions={[10, 25, 50]}
                emptyMessage="No Data found." tableStyle={{ minWidth: '50rem' }} sortField="applicationDetails.maskId"
                sortOrder={-1}>
                <Column field="dueStatus" header="Due Status" body={dueStatusBosyTemplate} filterField="dueStatus" filterMenuStyle={{ width: '14rem' }} filter filterElement={dueStatusFilterTemplate} showFilterMatchModes={false} style={{ width: '15%' }} ></Column>
                <Column field="id" body={maskIdBodyTemplateForReport} header="ID" sortable style={{ width: '15%' }} ></Column>
                <Column field="state" header="Required Action" body={requiredBodyAction} showFilterMatchModes={false} ></Column>
                <Column field="applcationDetails.locationFour.name" header="Project / DC" body={projectBodyTemplateForReport}></Column>
                <Column field="dueDate" header="Due Date" body={dueBodyTemplate} style={{ width: '15%' }} ></Column>
            </DataTable>

            {reportModal && <ReportDataModal data={actionOne} applicationType={applicationType} showModal={reportModal} setShowModal={setReportModal} />}
            {reviewModal && <ReviewDataModal data={actionOne} last={actionOneLast} applicationType={applicationType} showModal={reviewModal} setShowModal={setReviewModal} />}
        </>
    );
}

export default ActionCard;
