import React, { useEffect, useRef, useState } from "react";
import { Mo<PERSON>, But<PERSON>, Form, Card, Row, Col, Container } from 'react-bootstrap';
import moment from "moment";
import GalleryPage from '../apps/Gallery';
import API from "../services/API";
import { API_URL, STATIC_URL, USERS_URL, DYNAMIC_TITLES_URL, EPTW_CHECKLIST, LOCATION_FIVE, LOCATION_SIX, FILE_URL, PERMIT_ACTION_REPORT_WITH_ID, PERMIT_ACTION_REPORT_REJECT_WITH_ID, DCSO_APPROVER_LIST, CLOSEOUT_PERMIT, NORMALIZE_PERMIT, WITHDRAW_PERMIT, SUSPEND_PERMIT } from "../constants";
import useForceUpdate from "use-force-update";
// import { permitChecklist } from "./ptwData";
import { Button as Button1 } from 'primereact/button';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import SelectCardComponent from "./Eptw/component/SelectCardComponent";
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import customFontRegularBase64 from '../Font64'
import LatoBold from '../../assets/font/lato/Lato-Bold.ttf'
import QRCode from "react-qr-code";
import SignatureCanvas from 'react-signature-canvas'
import { InputGroup } from "react-bootstrap";
import Swal from "sweetalert2";
import { useSelector } from "react-redux";
import Select from 'react-select';
pdfMake.vfs = pdfFonts.pdfMake.vfs;




const customFontBoldBase64 = LatoBold;

console.log(customFontRegularBase64)

const PermitModal = ({ applicationDetails, reportData, showReportModal, setShowReportModal }) => {

    console.log(applicationDetails)
    const [permitChecklist, setPermitChecklist] = useState([])
    const user = useSelector((state) => state.login.user);
    const [applicantSign, setApplicantSign] = useState('');
    const [applicantSign1, setApplicantSign1] = useState('');
    const [assessorSign, setAssessorSign] = useState('');
    const [approverSign, setApproverSign] = useState('');
    const [dcsoSign, setDcsoSign] = useState('');
    const [searchModal, setSearchModal] = useState(false)
    const [dcsoNormSignedDate, setDcsoNormSignedDate] = useState('')
    const [applicantCloseSign, setApplicantCloseSign] = useState('');

    //  const [informFireDept, setInformFireDept] = useState('')
    //   const [fireDeptDetails, setFireDeptDetails] = useState('')
    //   const [informFireInsurance, setInformFireInsurance] = useState('')
    //   const [fireInsuranceDetails, setFireInsuranceDetails] = useState('')

    const [signs, setSign] = useState('')
    const [apiStatus, setApiStatus] = useState('Approve')
    const [locationOne, setLocationOne] = useState([])
    const [locationTwo, setLocationTwo] = useState([])
    const [locationThree, setLocationThree] = useState([])
    const [locationFour, setLocationFour] = useState([])
    const [locationFive, setLocationFive] = useState([])
    const [locationSix, setLocationSix] = useState([])
    const [locationSearchOne, setLocationSearchOne] = useState([])
    const [locationSearchTwo, setLocationSearchTwo] = useState([])
    const [locationSearchThree, setLocationSearchThree] = useState([])
    const [locationSearchFour, setLocationSearchFour] = useState([])
    const [locationSearchFive, setLocationSearchFive] = useState([])
    const [locationSearchSix, setLocationSearchSix] = useState([])
    const [applcationDetail, setApplicationDetail] = useState(applicationDetails || '')
    const [isoModal, setIsoModal] = useState(false)
    const [isoSSModal, setIsoSSModal] = useState(false)
    const [isoEnergyModal, setIsoEnergyModal] = useState(false)
    const [title, setTitle] = useState([])
    const forceUpdate = useForceUpdate()
    const [dcsoComments, setDcsoComments] = useState('')
    const [dcsoApprover, setDcsoApprover] = useState('')
    const [selectedDcso, setSelectedDcso] = useState('')
    const [selectedApprover, setSelectedApprover] = useState(null);
    const fire = [{ systems: [{ label: 'Pre-Action system', checked: 0 }, { label: 'Vesda', checked: 0 }, { label: 'Smoke Detector', checked: 0 }, { label: 'Gas Suppression', checked: 0 }, { label: 'Water Mist', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: '', loc6: '' }]

    const security = [{
        systems: [{ label: 'Door Alarm', checked: 0 }, { label: 'Door Access', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}
    }]

    const [energySysLocations, setEnergySysLocations] = useState([{ systems: [{ label: 'Electrical', checked: 0 }, { label: 'Mechanical', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: {}, loc6: {}, systemName: [] }])

    const [isChecked, setIsChecked] = useState(false);
    const [informFireDept, setInformFireDept] = useState('');
    const [fireDeptDetails, setFireDeptDetails] = useState('');
    const [informFireInsurance, setInformFireInsurance] = useState('');
    const [fireInsuranceDetails, setFireInsuranceDetails] = useState('');

    const handleSwitchChange = (e) => {
        setIsChecked(e.target.checked);
        console.log('Switch is now:', e.target.checked ? 'On' : 'Off');
    };

    // Handle fire department radio button change
    const handleFireDeptChange = (value) => {
        setInformFireDept(value);
        // Clear details if "No" is selected
        if (value === 'No') {
            setFireDeptDetails('');
        }
    };

    // Handle fire insurance radio button change
    const handleFireInsuranceChange = (value) => {
        setInformFireInsurance(value);
        // Clear details if "No" is selected
        if (value === 'No') {
            setFireInsuranceDetails('');
        }
    };

    console.log(reportData)
    const [dcop, setDcop] = useState(reportData?.dcop || []);
    const highRisk = reportData?.high_risk || [];
    const uploads = reportData?.uploads || [];
    const [fireSystems, setFireSystems] = useState(reportData?.dcop?.fireSystems || []);
    const [fireSysMore, setFireSysMore] = useState(reportData?.dcop?.fireSysMore || [])
    // const securitySystems = dcop?.securitySystems || [];
    const [securitySystems, setSecuritySystems] = useState(dcop?.securitySystems || []);
    const [highRiskApprover, setHighRiskApprover] = useState([])
    const [energySystems, setEnergySystems] = useState(dcop?.energySystems || []);
    const sign = useRef()
    const permits = reportData?.high_risk?.selectedPermits || [];
    const checklists = reportData?.high_risk?.checklists || [];
    const [fs, setFS] = useState([])
    const [ss, setSS] = useState([])
    const [es, setES] = useState([])
    useEffect(() => {
        setFireSystems(reportData?.dcop?.fireSystems || []);
    }, [reportData?.dcop]);
    useEffect(() => {
        setSecuritySystems(dcop?.securitySystems || []);
    }, [dcop]);
    useEffect(() => {
        setEnergySystems(dcop?.energySystems || []);
    }, [dcop]);
    useEffect(() => {
        getAllUsers();
        getTitle();
        getPermitChecklist();
    }, [])
    const getTitle = async () => {
        const response = await API.get(DYNAMIC_TITLES_URL);
        if (response.status === 200) {
            setTitle(response.data)
        }

    }
    const getPermitChecklist = async () => {
        const response = await API.get(EPTW_CHECKLIST);
        if (response.status === 200) {
            setPermitChecklist(response.data)
        }
    }
    const getDsco = async () => {
        const reqData = {}

        reqData['locationOneId'] = reportData.locationOneId
        reqData['locationTwoId'] = reportData.locationTwoId
        reqData['locationThreeId'] = reportData.locationThreeId
        reqData['locationFourId'] = reportData.locationFourId
        const response = await API.post(DCSO_APPROVER_LIST, reqData);
        if (response.status === 200) {
            const formattedData = response.data.map((item) => ({
                label: item.firstName,  // or item.label if your data has a different key
                value: item.id,    // or item.value if your data has a different key
            }));

            setDcsoApprover(formattedData)

        }
    }
    useEffect(() => {
        const loadSignatures = async () => {
            try {
                if (reportData.high_risk?.applicantSign) {
                    console.log(1)
                    const assessorSignURL = `${STATIC_URL}/${reportData.high_risk.applicantSign}`;
                    const assessorSignDataURL = await fetchApplicantSign(assessorSignURL);
                    setApplicantSign1(assessorSignDataURL);
                }
                if (dcop?.applicantSign) {
                    const applicantSignURL = `${STATIC_URL}/${dcop.applicantSign}`;
                    const applicantSignDataURL = await fetchApplicantSign(applicantSignURL);
                    setApplicantSign(applicantSignDataURL);
                }

                if (reportData.high_risk?.assessorSign) {
                    const assessorSignURL = `${STATIC_URL}/${reportData.high_risk.assessorSign}`;
                    const assessorSignDataURL = await fetchApplicantSign(assessorSignURL);
                    setAssessorSign(assessorSignDataURL);
                }


                if (reportData.high_risk?.approverSign) {
                    const approverSignURL = `${STATIC_URL}/${reportData.high_risk.approverSign}`;
                    const approverSignDataURL = await fetchApplicantSign(approverSignURL);
                    setApproverSign(approverSignDataURL);
                }

                if (dcop?.dcsoSign) {
                    const dcsoSignURL = `${STATIC_URL}/${dcop.dcsoSign}`;
                    const dcsoSignDataURL = await fetchApplicantSign(dcsoSignURL);
                    setDcsoSign(dcsoSignDataURL);
                }
                if (dcop?.approverSign) {
                    const dcsoSignURL = `${STATIC_URL}/${dcop.approverSign}`;
                    const dcsoSignDataURL = await fetchApplicantSign(dcsoSignURL);
                    setApproverSign(dcsoSignDataURL);
                }
                if (reportData?.closure?.applicantSign) {
                    const dcsoSignURL = `${STATIC_URL}/${reportData?.closure.applicantSign}`;
                    const dcsoSignDataURL = await fetchApplicantSign(dcsoSignURL);
                    setApplicantCloseSign(dcsoSignDataURL);
                }


                if (reportData?.closure?.dcso) {
                    const final = reportData?.closure?.dcso.find(item => item.status === "Normalized")
                    const dcsoSignURL = `${STATIC_URL}/${final.dcsoNormSign}`;
                    const dcsoSignDataURL = await fetchApplicantSign(dcsoSignURL);
                    setDcsoNormSignedDate(dcsoSignDataURL);
                }
            } catch (error) {
                console.error('Error loading signatures:', error);
            }
        };

        if (reportData) {
            getDsco()
            loadSignatures();
        }

    }, [dcop, reportData])

    const [users, setUsers] = useState([])
    const getAllUsers = async () => {
        const response = await API.get(USERS_URL);
        setUsers(response.data)
    }
    const addNewFS = () => {
        let locfs = JSON.parse(JSON.stringify(fs))
        locfs.push({ systems: [{ label: 'Pre-Action system', checked: 0 }, { label: 'Vesda', checked: 0 }, { label: 'Smoke Detector', checked: 0 }, { label: 'Gas Suppression', checked: 0 }, { label: 'Water Mist', checked: 0 }, { label: 'Others', checked: 0, others: '' }], loc5: '', loc6: '' })
        setFS(locfs)
    }
    function getName(id) {
        const user = users.find(user => user.id === id)
        return id ? user.firstName : ''
    }

    function getConfirmationLabel(data, checklistId) {

        if (!checklists[checklistId] || !checklists[checklistId][data.id]) {
            return 'N/A';
        }

        const checklist = checklists[checklistId][data.id];
        if (!checklist.options) {
            return 'N/A';
        }

        const option = checklist.options.find(option => option.checked === 1);
        return option ? option.label : 'N/A';
    }


    function getRemarks(data, checklistId) {
        if (!checklists[checklistId] || !checklists[checklistId][data.id]) {
            return 'N/A';
        }
        const option = checklists[checklistId][data.id].remarks;
        return option ? option : 'N/A';
    }
    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    function getPersonnel(data, checklistId) {
        if (!checklists[checklistId] || !checklists[checklistId][data.id]) {
            return 'N/A';
        }
        const option = checklists[checklistId][data.id].personnel;
        return option ? option : 'N/A';
    }

    useEffect(() => {
        const fetchLocationFive = async () => {
            if (reportData.locationFourId) {
                try {
                    const response = await API.get(LOCATION_FIVE(reportData.locationFourId));
                    if (response.status === 200) {
                        setLocationFive(response.data);
                        setLocationSearchFive(response.data);
                    }
                } catch (error) {
                    console.error('Error fetching location five:', error);
                    // Handle error appropriately
                }
            }
        };

        fetchLocationFive();
    }, [reportData.locationFourId]);

    useEffect(() => {
        if (reportData.status === "Pending HRA Assessment") {
            fetchHighRiskApprover();
        } else if (reportData.status === "Pending HRA Approval") {
            fetchDscoRepersentive();
        }

    }, [reportData.locationFourId]);

    const fetchHighRiskApprover = async () => {

        try {
            const response = await API.post('users/eptw-high-risk-approver', {
                locationOneId: reportData.locationOne.id,
                locationTwoId: reportData.locationTwo.id,
                locationThreeId: reportData.locationThree.id,
                locationFourId: reportData.locationFour.id,
            });
            if (response.status === 200 && Array.isArray(response.data)) {
                const formatted = response.data.map(user => ({
                    label: user.firstName,
                    value: user.id
                }));


                setHighRiskApprover(formatted);

            }
        } catch (error) {
            console.error('Error fetching location five:', error);
            // Handle error appropriately
        }

    };

    const fetchDscoRepersentive = async () => {

        try {
            const response = await API.post('users/eptw-dcso-approver', {
                locationOneId: reportData.locationOne.id,
                locationTwoId: reportData.locationTwo.id,
                locationThreeId: reportData.locationThree.id,
                locationFourId: reportData.locationFour.id,
            });
            if (response.status === 200 && Array.isArray(response.data)) {
                const formatted = response.data.map(user => ({
                    label: user.firstName,
                    value: user.id
                }));


                setHighRiskApprover(formatted);

            }
        } catch (error) {
            console.error('Error fetching location five:', error);
            // Handle error appropriately
        }

    };


    const fetchLocationSix = async (id) => {

        try {
            const response = await API.get(LOCATION_SIX(id));
            if (response.status === 200) {
                setLocationSix(response.data);
                setLocationSearchSix(response.data);
            }
        } catch (error) {
            console.error('Error fetching location five:', error);
            // Handle error appropriately
        }

    };

    const searchValue = (name, type) => {
        if (type === 'one') {
            const one = locationSearchOne
            setLocationOne(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'two') {
            const one = locationSearchTwo
            setLocationTwo(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'three') {
            const one = locationSearchThree
            setLocationThree(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'four') {
            const one = locationSearchFour
            setLocationFour(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'five') {
            const one = locationSearchFive
            setLocationFive(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }
        else if (type === 'six') {
            const one = locationSearchSix
            setLocationSix(one.filter((i) => { return i.name.toLowerCase().includes(name.toLowerCase()) }))
        }



    }
    const isImage = (filename) => {
        const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp'];
        const extension = filename.split('.').pop().toLowerCase();
        return imageExtensions.includes(extension);
    };
    const generatePdf = () => {
        const input = document.getElementById('pdf-content');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const marginBottom = 10; // margin at the bottom of each page

        html2canvas(input).then((canvas) => {
            const imgData = canvas.toDataURL('image/png');
            const imgProps = pdf.getImageProperties(imgData);
            const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
            heightLeft -= pdfHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight + marginBottom;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
                heightLeft -= pdfHeight;
            }

            pdf.save('permit-report.pdf');
        });
    }
    const convertToDataUrl = async (url) => {
        return fetch(url)
            .then(response => response.blob())
            .then(blob => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        resolve(reader.result);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            });
    };



    const fetchApplicantSign = async (url) => {
        console.log(url)
        try {
            const url1 = await convertToDataUrl(url);


            return url1;
        } catch (error) {
            console.error('Error converting to data URL:', error);
            return null;
        }
    };
    const deleteFS = (index) => {
        let locfs = JSON.parse(JSON.stringify(fs))
        locfs.splice(index, 1)
        setFS(locfs)
    }


    const deleteSS = (index) => {
        let locss = JSON.parse(JSON.stringify(ss))
        locss.splice(index, 1)
        setSS(locss)
    }

    const tagComponent = (item) => {
        return (
            <div className="m-1" >
                <div onClick={() => { item.checked = item.checked === 0 ? 1 : 0; forceUpdate() }} className=" d-flex p-2" style={{ border: '1px solid #00000050', textWrap: 'nowrap', position: 'relative', borderRadius: '20px', width: 'fit-content', cursor: 'pointer' }}>
                    <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked === 1 ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                    {item.checked === 1 &&
                        <i className="material-icons" style={{ position: 'absolute', color: 'white' }} >check</i>

                    }
                    <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                </div>
                {item.label === 'Others' && item.checked === 1 &&
                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-start text-start mt-2">
                        <InputGroup className="mb-3">
                            <Form.Control
                                placeholder=""
                                type="text"
                                onChange={(e) => item.others = e.target.value}
                            />

                        </InputGroup>
                    </Col>
                }
            </div>
        )
    }
    const handleIsolationChange = (systemIndex, itemIndex, newIsolated) => {
        const updatedFireSystems = [...fireSystems];
        updatedFireSystems[systemIndex].systems[itemIndex].isolated = newIsolated;
        updatedFireSystems[systemIndex].systems[itemIndex].remarks = ''; // Clear remarks when changing isolation status
        setFireSystems(updatedFireSystems);
    };

    const handleRemarksChange = (systemIndex, itemIndex, newRemarks) => {
        const updatedFireSystems = [...fireSystems];
        updatedFireSystems[systemIndex].systems[itemIndex].remarks = newRemarks;
        setFireSystems(updatedFireSystems);
    };


    const handleIsolationSecurityChange = (systemIndex, itemIndex, newIsolated) => {
        const updatedSecuritySystems = [...securitySystems];
        updatedSecuritySystems[systemIndex].systems[itemIndex].isolated = newIsolated;
        updatedSecuritySystems[systemIndex].systems[itemIndex].remarks = ''; // Clear remarks when changing isolation status
        setSecuritySystems(updatedSecuritySystems);
    };

    const handleIsolationEnergyChange = (systemIndex, itemIndex, newIsolated) => {
        const updatedSecuritySystems = [...energySystems];
        updatedSecuritySystems[systemIndex].systems[itemIndex].isolated = newIsolated;
        updatedSecuritySystems[systemIndex].systems[itemIndex].remarks = ''; // Clear remarks when changing isolation status
        setEnergySystems(updatedSecuritySystems);
    };

    const handleSecurityRemarksChange = (systemIndex, itemIndex, newRemarks) => {
        const updatedSecuritySystems = [...securitySystems];
        updatedSecuritySystems[systemIndex].systems[itemIndex].remarks = newRemarks;
        setSecuritySystems(updatedSecuritySystems);
    };

    const validateFS = (data) => {
        const errors = {};

        data.forEach((item, index) => {
            const checkedSystems = item.systems.filter((system) => system.checked === 1);

            // Validate at least one system is selected
            if (checkedSystems.length === 0) {
                errors[`systems-${index}`] = 'At least one system must be selected.';
            }

            // Validate 'Others' if selected
            const othersSystem = checkedSystems.find((system) => system.label === 'Others');
            if (othersSystem && !othersSystem.others) {
                errors[`others-${index}`] = 'Please provide details for "Others".';
            }

            // Validate location selections
            if (!item.loc5 || !item.loc5.id) {
                errors[`loc5-${index}`] = 'Location of the system(s) is required.';
            }
            if (!item.loc6 || !item.loc6.id) {
                errors[`loc6-${index}`] = 'Location 6 is required.';
            }
        });

        return Object.keys(errors).length === 0;
    };
    const setSecuritySystemsIsolation = () => {
        console.log(ss)

        if (validateFS(ss)) {
            const newData = ss[0].systems.filter((system) => system.checked === 1);
            console.log(newData)

            const newSecuritySystem = {
                ...ss[0],
                systems: newData.map((system) => ({
                    label: system.label,
                    isolated: 1,
                    remarks: '', // Set remarks to empty or adjust as needed
                    ...(system.label === 'Others' && { others: system.others }) // Include 'others' field only if it's an 'Others' system
                })),
                isNew: true
            };

            console.log(newSecuritySystem)
            // Append the new data to the existing security systems state
            setSecuritySystems((prevSecuritySystems) => [...prevSecuritySystems, newSecuritySystem])
            setIsoSSModal(false)
        } else {
            Swal.fire({
                title: '',
                text: "Please fill all the required fields.",
                icon: 'warning',
            })

        }
        if (dcop.isDcSecurity === 'No') {
            setDcop((prevData) => ({
                ...prevData,
                isDcSecurity: "Yes"
            }))
        }

    }

    const setEnergySystemsIsolation = () => {
        console.log(es)

        if (validateFS(es)) {
            const newData = es[0].systems.filter((system) => system.checked === 1);
            console.log(newData)

            const newSecuritySystem = {
                ...es[0],
                systems: newData.map((system) => ({
                    label: system.label,
                    isolated: 1,
                    remarks: '', // Set remarks to empty or adjust as needed
                    ...(system.label === 'Others' && { others: system.others }) // Include 'others' field only if it's an 'Others' system
                })),
                isNew: true
            };

            console.log(newSecuritySystem)
            // Append the new data to the existing security systems state
            setEnergySystems((prevSecuritySystems) => [...prevSecuritySystems, newSecuritySystem])
            setIsoEnergyModal(false)
        } else {
            Swal.fire({
                title: '',
                text: "Please fill all the required fields.",
                icon: 'warning',
            })

        }
        if (dcop.isDcEnergy === 'No') {
            setDcop((prevData) => ({
                ...prevData,
                isDcEnergy: "Yes"
            }))
        }

    }
    const setFireSystemsIsolation = () => {

        if (dcop.isDcFire === 'No') {
            setDcop((prevData) => ({
                ...prevData,
                isDcFire: "Yes"
            }))
        }

        if (validateFS(fs)) {

            const newData = fs[0].systems.filter((system) => system.checked === 1);
            console.log(newData)

            const newSecuritySystem = {
                ...fs[0],
                systems: newData.map((system) => ({
                    label: system.label,
                    isolated: 1,
                    remarks: '', // Set remarks to empty or adjust as needed
                    ...(system.label === 'Others' && { others: system.others }) // Include 'others' field only if it's an 'Others' system
                })),
                isNew: true
            };

            console.log(newSecuritySystem)
            // Append the new data to the existing security systems state
            setFireSystems((prevSecuritySystems) => [...prevSecuritySystems, newSecuritySystem])
            setIsoModal(false)
        } else {
            Swal.fire({
                title: '',
                text: "Please fill all the required fields.",
                icon: 'warning',
            })

        }
    }

    const handleRemoveSystem = (systemIndex) => {
        const updatedSecuritySystems = securitySystems.filter((_, index) => index !== systemIndex);
        setSecuritySystems(updatedSecuritySystems);
    };

    const handleFSRemoveSystem = (systemIndex) => {
        const updatedSecuritySystems = fireSystems.filter((_, index) => index !== systemIndex);
        setFireSystems(updatedSecuritySystems);
    };
    const config = {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    };
    const submitObs = async (status) => {

        if (status === 'Approve') {
            let signUrl = ''
            try {

                const formData = new FormData();

                formData.append('file', dataURItoFile(signs, 'sign.png'));


                const fileResponse = await API.post(FILE_URL, formData, config)
                if (fileResponse.status === 200) {
                    signUrl = fileResponse.data.files[0].originalname;

                }
            }
            catch (e) {
                console.log(e)
            }

            var reqData = {}
            var dcOpsData = Object.assign({}, dcop)
            if (dcop.isDcFire == 'Yes') {
                dcOpsData['fireSystems'] = fireSystems
            }
            if (dcop.isDcSecurity == 'Yes') {
                dcOpsData['securitySystems'] = securitySystems
            }
            if (dcop.isDcSecurity == 'Yes') {
                dcOpsData['securitySystems'] = securitySystems
            }
            if (dcop.isDcEnergy == 'Yes') {
                dcOpsData['EnergySystems'] = energySystems
            }

            // Add fire department and fire insurance information
            dcOpsData['informFireDept'] = informFireDept
            if (informFireDept === 'Yes') {
                dcOpsData['fireDeptDetails'] = fireDeptDetails
            }

            dcOpsData['informFireInsurance'] = informFireInsurance
            if (informFireInsurance === 'Yes') {
                dcOpsData['fireInsuranceDetails'] = fireInsuranceDetails
            }

            dcOpsData['dcsoSign'] = signUrl
            dcOpsData['dcsoComments'] = dcsoComments
            dcOpsData['dcsoSignedDate'] = moment().format('DD-MM-YYYY hh:mm A')

            console.log(dcOpsData)
            reqData['dcop'] = JSON.stringify(dcOpsData)

            reqData['status'] = 'Active'

            const response = await API.patch(PERMIT_ACTION_REPORT_WITH_ID(reportData?.id, applcationDetail?.id),
                JSON.stringify(reqData)
            );
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "Isolation Completed and Permit has been Approved.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();


                    }
                })
            }

        } else {
            var reqData = {}
            var dcOpsData = Object.assign({}, dcop)

            dcOpsData['dcsoComments'] = dcsoComments
            dcOpsData['rejectedBy'] = user.firstName

            reqData['dcop'] = JSON.stringify(dcOpsData)
            reqData['status'] = 'Rejected'

            const response = await API.patch(PERMIT_ACTION_REPORT_REJECT_WITH_ID(reportData?.id, applcationDetail?.id),
                JSON.stringify(reqData)
            );
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "This Permit has been Rejected.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.reload();

                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            }
        }
    }

    const submitNormalize = async (status) => {

        if (status === 'Approve') {
            let signUrl = ''
            try {

                const formData = new FormData();

                formData.append('file', dataURItoFile(signs, 'sign.png'));


                const fileResponse = await API.post(FILE_URL, formData, config)
                if (fileResponse.status === 200) {
                    signUrl = fileResponse.data.files[0].originalname;

                }
            }
            catch (e) {
                console.log(e)
            }

            var reqData = {}
            var clos = JSON.parse(JSON.stringify(reportData?.closure))
            clos['status'] = 'Closed'//'Normalized'
            let normObj = clos?.dcso || []
            console.log('1')
            // let dcso = JSON.parse(JSON.stringify(normObj[userId]))
            let dcso = normObj.length == 0 ? {} : normObj[normObj.length - 1] //JSON.parse(JSON.stringify(normObj[normObj.length - 1]))
            dcso['status'] = 'Normalized'
            dcso['normalizedDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')
            dcso['dcsoNormSign'] = signUrl
            dcso['dcsoNormComments'] = dcsoComments
            dcso['dcsoNormSignedDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')
            normObj.length == 0 ? normObj.push(dcso) : normObj[normObj.length - 1] = dcso
            console.log('2')
            // normObj[normObj.length - 1] = dcso
            clos['dcso'] = normObj
            console.log('3')

            reqData['closure'] = JSON.stringify(clos)

            const response = await API.patch(NORMALIZE_PERMIT(reportData?.id),
                JSON.stringify(reqData)
            );
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "DCSO action is completed and the permit is closed.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();


                    }
                })
            }

        } else {
            var reqData = {}
            var clos = JSON.parse(JSON.stringify(reportData?.closure))

            clos['status'] = 'Return'//'Return'
            // clos['oldDcso'] = clos.newDcso
            let normObj = clos?.dcso || []
            // let dcso = JSON.parse(JSON.stringify(normObj[userId]))
            let dcso = normObj.length == 0 ? {} : normObj[normObj.length - 1]
            dcso['status'] = 'Returned'
            dcso['dcsoReturnedDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')
            dcso['dcsoNormComments'] = dcsoComments
            normObj.length == 0 ? normObj.push(dcso) : normObj[normObj.length - 1] = dcso
            clos['dcso'] = normObj
            reqData['status'] = 'Return'

            reqData['closure'] = JSON.stringify(clos)

            const response = await API.patch(NORMALIZE_PERMIT(reportData?.id),
                JSON.stringify(reqData)
            );
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "This permit has been returned to the applicant.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.reload();

                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            }
        }
    }
    const validationAssessor = () => {
        if (apiStatus === 'Approve') {
            if (!signs || !selectedApprover?.value) {
                Swal.fire({
                    title: '',
                    text: "Please fill all the required fields.",
                    icon: 'warning',
                })
            } else {
                submitAssessor('Approved')
            }
        } else if (apiStatus === 'Reject') {
            if (!dcsoComments) {
                Swal.fire({
                    title: '',
                    text: "Please fill all the required fields.",
                    icon: 'warning',
                })
            } else {
                submitAssessor('Rejected')
            }
        }

    }

    const submitAssessor = async (status) => {

        if (status === 'Approved') {
            let signUrl = ''
            try {

                const formData = new FormData();

                formData.append('file', dataURItoFile(signs, 'sign.png'));


                const fileResponse = await API.post(FILE_URL, formData, config)
                if (fileResponse.status === 200) {
                    signUrl = fileResponse.data.files[0].originalname;

                }
            }
            catch (e) {
                console.log(e)
            }

            if (reportData.status === "Pending HRA Assessment") {
                var reqData = {}
                var highRiskData = Object.assign({}, highRisk)
                highRiskData['assessorSign'] = signUrl
                highRiskData['assessorSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
                highRiskData['assessorComments'] = dcsoComments
                highRiskData['approverName'] = selectedApprover?.name// || selectedApprover?.title

                reqData['high_risk'] = JSON.stringify(highRiskData)
                reqData['approverId'] = selectedApprover.value

                const response = await API.patch('permit-reports-approver/' + reportData?.id + '/' + applcationDetail?.id,
                    JSON.stringify(reqData)
                );
                if (response.status === 204) {

                    Swal.fire({
                        title: '',
                        text: "Submitted to Approver.",
                        icon: 'success',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                } else {

                    Swal.fire({
                        title: '',
                        text: "Please Try Again.",
                        icon: 'warning',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                }
            } else if (reportData.status === "Pending HRA Approval") {

                if (dcop.highRisk === 'Yes') {
                    var reqData = {}
                    var highRiskData = Object.assign({}, highRisk)
                    var dcOpsData = Object.assign({}, dcop)
                    highRiskData['approverSign'] = signUrl
                    highRiskData['approverSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
                    highRiskData['approverComments'] = dcsoComments
                    dcOpsData['dcsoName'] = selectedApprover?.firstName
                    reqData['dcsoApproverId'] = selectedApprover.value

                    reqData['high_risk'] = JSON.stringify(highRiskData)
                    reqData['dcop'] = JSON.stringify(dcOpsData)
                    const response = await API.patch('permit-reports-dsco-approver/' + reportData?.id + '/' + applcationDetail?.id,
                        JSON.stringify(reqData)
                    );
                    if (response.status === 204) {

                        Swal.fire({
                            title: '',
                            text: "High Risk activities in this Permit has been Approved and forwarded to the DCSO for Isolation.",
                            icon: 'success',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // navigate(-1)
                                window.location.reload();

                            }
                        })
                    } else {

                        Swal.fire({
                            title: '',
                            text: "Please Try Again.",
                            icon: 'warning',

                        }).then((result) => {
                            if (result.isConfirmed) {
                                // navigate(-1)
                                window.location.reload();

                            }
                        })
                    }
                }
            } else {

                dcOpsData['approverSign'] = signUrl
                dcOpsData['approverSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')
                dcOpsData['approverComments'] = dcsoComments
                dcOpsData['dcsoName'] = selectedApprover?.firstName
                reqData['dcsoApproverId'] = selectedApprover.value
                reqData['dcop'] = JSON.stringify(dcOpsData)


                const response = await API.patch('permit-reports-dsco-approver/' + reportData?.id + '/' + applcationDetail?.id,
                    JSON.stringify(reqData)
                );
                if (response.status === 204) {

                    Swal.fire({
                        title: '',
                        text: "This Permit has been Approved and forwarded to the DCSO for Isolation.",
                        icon: 'success',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                } else {

                    Swal.fire({
                        title: '',
                        text: "Please Try Again.",
                        icon: 'warning',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                }


            }
        } else {

            if (reportData.status === "Pending HRA Assessment") {
                var reqData = {}
                var highRiskData = Object.assign({}, highRisk)
                var dcOpsData = Object.assign({}, dcop)
                highRiskData['assessorComments'] = dcsoComments
                highRiskData['assessorSignedDate'] = moment(new Date()).format('DD-MM-YYYY hh:mm A')

                reqData['high_risk'] = JSON.stringify(highRiskData)
                reqData['status'] = 'Rejected'
                dcOpsData['rejectedBy'] = user?.firstName

                const response = await API.patch('permit-reports-reject/' + (reportData?.id) / (applcationDetail?.id),
                    JSON.stringify(reqData)
                );
                if (response.status === 204) {

                    Swal.fire({
                        title: '',
                        text: "This Permit has been Rejected.",
                        icon: 'success',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                } else {

                    Swal.fire({
                        title: '',
                        text: "Please Try Again.",
                        icon: 'warning',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                }
            } else if (reportData.status === "Pending HRA Approval") {
                if (dcop.highRisk === 'Yes') {
                    var highRiskData = Object.assign({}, highRisk)
                    highRiskData['approverComments'] = dcsoComments
                    reqData['high_risk'] = JSON.stringify(highRiskData)
                } else {
                    dcOpsData['approverComments'] = dcsoComments
                    reqData['dcop'] = JSON.stringify(dcOpsData)

                }
                const response = await API.patch('permit-reports-reject/' + (reportData?.id) / (applcationDetail?.id),
                    JSON.stringify(reqData)
                );
                if (response.status === 204) {

                    Swal.fire({
                        title: '',
                        text: "This Permit has been Rejected.",
                        icon: 'success',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                } else {

                    Swal.fire({
                        title: '',
                        text: "Please Try Again.",
                        icon: 'warning',

                    }).then((result) => {
                        if (result.isConfirmed) {
                            // navigate(-1)
                            window.location.reload();

                        }
                    })
                }

            }
        }
    }





    const validationCheck = () => {
        if (apiStatus === 'Approve') {
            const hasFireSysIsolated = fireSystems?.every((dt) => dt.systems.every(item => (item.isolated == 1) || (item.isolated == -1 && item?.remarks)))//fireSystems.every(item => item.isolated === 1);
            const hasSecuritySysisolated = securitySystems?.every((dt) => dt.systems.every(item => (item.isolated == 1) || (item.isolated == -1 && item?.remarks)))//securitySystems.every(item => item.isolated === 1);

            const hasEnergySysisolated = energySystems?.every((dt) => dt.systems.every(item => (item.isolated == 1)))
            console.log('hasFireSysIsolated', hasFireSysIsolated)
            console.log('hasSecuritySysIsolated', hasSecuritySysisolated)
            console.log('hasEnergySysIsolated', hasEnergySysisolated)
            console.log('firesystems', JSON.stringify(fireSystems))

            // Validate fire department details if "Yes" is selected
            const isFireDeptValid = informFireDept !== 'Yes' || (informFireDept === 'Yes' && fireDeptDetails.trim() !== '');

            // Validate fire insurance details if "Yes" is selected
            const isFireInsuranceValid = informFireInsurance !== 'Yes' || (informFireInsurance === 'Yes' && fireInsuranceDetails.trim() !== '');

            // Check if fire department and fire insurance options are selected
            const isFireDeptSelected = informFireDept === 'Yes' || informFireDept === 'No';
            const isFireInsuranceSelected = informFireInsurance === 'Yes' || informFireInsurance === 'No';

            if (!signs ||
                (dcop.isDcFire === 'Yes' && !hasFireSysIsolated) ||
                (dcop.isDcSecurity === 'Yes' && !hasSecuritySysisolated) ||
                (dcop.isDcEnergy === 'Yes' && !hasEnergySysisolated) ||
                !isFireDeptSelected ||
                !isFireInsuranceSelected ||
                !isFireDeptValid ||
                !isFireInsuranceValid) {

                let errorMessage = "Please fill all the required fields.";

                if (!isFireDeptSelected) {
                    errorMessage = "Please select Yes or No for fire department notification.";
                } else if (informFireDept === 'Yes' && fireDeptDetails.trim() === '') {
                    errorMessage = "Please provide fire department details.";
                } else if (!isFireInsuranceSelected) {
                    errorMessage = "Please select Yes or No for fire insurance company notification.";
                } else if (informFireInsurance === 'Yes' && fireInsuranceDetails.trim() === '') {
                    errorMessage = "Please provide fire insurance company details.";
                }

                Swal.fire({
                    title: '',
                    text: errorMessage,
                    icon: 'warning',
                })
            }
            else {
                submitObs('Approve')
            }
        } else {
            if (!dcsoComments) {
                Swal.fire({
                    title: '',
                    text: "Please fill all the required fields.",
                    icon: 'warning',
                })
            } else {
                submitObs('Returned')
            }
        }

    }

    const validationCheckNormalization = () => {
        if (apiStatus === 'Approve') {

            if (!signs) {

                Swal.fire({
                    title: '',
                    text: "Please fill all the required fields.",
                    icon: 'warning',
                })

            }
            else {
                submitNormalize('Approve')
            }
        } else {
            if (!dcsoComments) {
                Swal.fire({
                    title: '',
                    text: "Please fill all the required fields.",
                    icon: 'warning',
                })
            } else {
                submitNormalize('Returned')
            }
        }


    }

    const ClosePermit = async () => {
        if (!isChecked || (reportData.closure?.status != 'Return' && !signs) || !selectedDcso?.value) {
            Swal.fire({
                title: '',
                text: "Please fill all the required fields.",
                icon: 'warning',
            })
        } else {
            let signUrl = ''
            if (reportData.closure?.status != 'Return') {
                try {
                    const formData = new FormData();

                    formData.append('file', dataURItoFile(signs, 'sign.png'));


                    const fileResponse = await API.post(FILE_URL, formData, config)
                    if (fileResponse.status === 200) {
                        signUrl = fileResponse.data.files[0].originalname;

                    }
                }
                catch (e) {
                    console.log(e)
                }
            }
            var closure = JSON.parse(JSON.stringify(reportData?.closure || {}))

            if (reportData.closure?.status == 'Return') {
                var normDcso = closure?.dcso || []
                normDcso.push({ 'id': selectedDcso?.id, 'name': selectedDcso?.name })
                // normDcso[selectedDcso?.id] = {'name': selectedDcso?.name}
                closure['dcso'] = normDcso//JSON.stringify(normDcso)
            }
            else {
                closure['status'] = 'Closed'
                closure['closeoutDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')
                closure['applicantSign'] = signUrl
                closure['applicantSignedDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')
                var normDcso = closure?.dcso || []
                normDcso.push({ 'id': selectedDcso?.value, 'name': selectedDcso?.label })
                closure['dcso'] = normDcso

                console.log(normDcso)
            }



            // closure['newDcso'] = selectedDcso?.name

            var reqData = {}
            reqData['closure'] = JSON.stringify(closure)
            reqData['newDcsoApproverId'] = selectedDcso?.value
            console.log('Req data', reqData)
            const response = await API.patch(CLOSEOUT_PERMIT(reportData.id), JSON.stringify(reqData));
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "This permit has been closed.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {

                        window.location.reload();

                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            }
        }
    }

    const withDraw = async () => {
        var reqData = {}
        reqData['status'] = 'Withdrawn'

        const response = await API.patch(WITHDRAW_PERMIT(reportData.id), JSON.stringify(reqData));
        if (response.status === 204) {

            Swal.fire({
                title: '',
                text: "This Permit has been Withdrawn.",
                icon: 'success',

            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.reload();
                }
            })
        } else {

            Swal.fire({
                title: '',
                text: "Please Try Again.",
                icon: 'warning',

            }).then((result) => {
                if (result.isConfirmed) {
                    // navigate(-1)
                    window.location.reload();

                }
            })
        }
    }

    const suspendPermit = async () => {

        if (!dcsoComments) {
            Swal.fire({
                title: '',
                text: "Please fill all the required fields.",
                icon: 'warning',
            })
        } else {
            var dcOpsData = Object.assign({}, dcop)
            dcOpsData['suspendComments'] = dcsoComments
            dcOpsData['suspendedDate'] = moment(new Date).format('DD-MM-YYYY hh:mm A')

            var reqData = {}
            reqData['dcop'] = JSON.stringify(dcOpsData)
            console.log('Req data', reqData)

            const response = await API.patch(SUSPEND_PERMIT(reportData.id), JSON.stringify(reqData));
            if (response.status === 204) {

                Swal.fire({
                    title: '',
                    text: "This permit has been suspended.",
                    icon: 'success',

                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.reload();
                    }
                })
            } else {

                Swal.fire({
                    title: '',
                    text: "Please Try Again.",
                    icon: 'warning',

                }).then((result) => {
                    if (result.isConfirmed) {
                        // navigate(-1)
                        window.location.reload();

                    }
                })
            }
        }

    }

    const getStatus = (status) => {
        if (status === 'Active') {
            const permitStartDate = moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A');
            const currentTime = moment();

            if (permitStartDate.isSameOrAfter(currentTime)) {
                return 'Active';
            } else {
                return 'Pending Work Commencement';
            }
        }
        return status
    }
    return (


        <>

            <Modal
                show={showReportModal}
                size={'lg'}
                onHide={() => setShowReportModal(false)}
                aria-labelledby="example-modal-sizes-title-md"
                id="pdf-content"
            >
                <Modal.Header>
                    {reportData && (
                        <div className="row" style={{ width: '100%' }}>
                            <div className="col-9">
                                <div className="row">
                                    <div className="col-3" style={{ borderRight: '1px solid #D1D5DB' }}>
                                        <img src={require("../../assets/images/logo.png")} className="me-3" alt="logo" style={{ maxWidth: '100%' }} />
                                    </div>
                                    <div className="col-9">
                                        <h4>Permit to Work</h4>
                                        <div className="d-flex align-items-center">
                                            <p className="me-2">#{reportData.maskId || ''} </p>
                                            <p className="card-eptw">{getStatus(reportData.status)} </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-3 d-flex justify-content-end align-items-center">
                                {(reportData.status === "Active" ||
                                    reportData.status === "Active: Timed Out" ||
                                    reportData.status === "Revoked: Pending DCSO Action" ||
                                    reportData.status === "Permit Overrun: Normalized and Closed" ||
                                    reportData.status === "Permit Overrun: Pending DCSO Action" ||
                                    reportData.status === "Normalized and Closed" ||
                                    reportData.status === "Closed: Pending DCSO Action" ||
                                    reportData.status === "Withdrawn" ||
                                    reportData.status === "Revoked" ||
                                    reportData.status === "Rejected" ||
                                    reportData.status === "Inactive: Timed Out" ||
                                    reportData.status === "Return" ||
                                    reportData.status === "Closed" ||
                                    reportData.status === "Permit Overrun: Pending Closure" ||
                                    reportData.status === "Application Timed Out") && (
                                        <Button1 type="button" label="Download Report" outlined icon="pi pi-download"
                                            // onClick={generatePdf}
                                            onClick={() => window.open(`${API_URL}/download-permit-pdf/${reportData.id}`, '_blank')}
                                        />
                                    )}
                            </div>

                        </div>
                    )}





                </Modal.Header>
                <Modal.Body>

                    {reportData && (
                        <div className="eptw-report-container" style={{ position: 'relative' }}>

                            {/* First Row */}
                            {(reportData.status === "Active" ||
                                reportData.status === "Active: Timed Out" ||
                                reportData.status === "Revoked: Pending DCSO Action" ||
                                reportData.status === "Permit Overrun: Pending DCSO Action" ||
                                reportData.status === "Permit Overrun: Normalized and Closed" ||
                                reportData.status === "Normalized and Closed" ||
                                reportData.status === "Closed: Pending DCSO Action" ||
                                reportData.status === "Withdrawn" ||
                                reportData.status === "Revoked" ||
                                reportData.status === "Rejected" ||
                                reportData.status === "Inactive: Timed Out" ||
                                reportData.status === "Return" ||
                                reportData.status === "Closed" ||
                                reportData.status === "Permit Overrun: Pending Closure" ||
                                reportData.status === "Application Timed Out") && (
                                    <div className="section" style={{ position: 'absolute', right: '0' }}>
                                        <QRCode
                                            value={`${API_URL}/download-permit-pdf/${reportData.id}`}
                                            size={150}
                                        />
                                    </div>
                                )}
                            <div className="section p-1">
                                <div className="row mb-3">
                                    {/* <div className="col-md-6">
                                        <p className="obs-title">ID</p>
                                        <p className="obs-content">{reportData.maskId || ''}</p>
                                    </div> */}
                                    <div className="col-md-6">
                                        <p className="obs-title">Applied On</p>
                                        <p className="obs-content">{moment(reportData.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Work Start Date & Time</p>
                                        <p className="obs-content">{moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Work End Date & Time</p>
                                        <p className="obs-content">{moment(reportData.permitEndDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm:ss A') || ''}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Applied By</p>
                                        <p className="obs-content">{reportData.applicant?.firstName || ''}</p>
                                    </div>
                                </div>

                            </div>
                            <div className="section p-1">
                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Location</p>
                                        <p className="obs-content">{(reportData.locationOne && reportData.locationTwo) && reportData.locationTwo.name + ', ' + reportData.locationOne.name}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Business Unit</p>
                                        <p className="obs-content">{reportData.locationThree && reportData.locationThree.name}</p>
                                    </div>
                                </div>

                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Project/DC name</p>
                                        <p className="obs-content">{reportData.locationFour && reportData.locationFour.name}</p>
                                    </div>
                                </div>
                                {reportData.zonesAndLevels && reportData.zonesAndLevels.length > 0 ? (
                                    <>
                                        {reportData.zonesAndLevels.map((item, i) => (
                                            <div className="row mb-3" key={i}>
                                                <div className="col-md-6">
                                                    <p className="obs-title">Level</p>
                                                    <p className="obs-content">{item.locationFive?.name}</p>
                                                </div>
                                                <div className="col-md-6">
                                                    <p className="obs-title">Zone</p>
                                                    <p className="obs-content">{item.locationSix?.name}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </>
                                ) : (
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Level</p>
                                            <p className="obs-content">{reportData.locationFive?.name}</p>
                                        </div>
                                        <div className="col-md-6">
                                            <p className="obs-title">Zone</p>
                                            <p className="obs-content">{reportData.locationSix?.name}</p>
                                        </div>
                                    </div>
                                )}





                                <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Type</p>
                                        <p className="obs-content">{reportData.permitType && reportData.permitType === 'CA' ? 'Construction' : reportData.permitType}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Status</p>
                                        <p className="obs-content">{reportData.status && reportData.status}</p>
                                    </div>
                                </div>

                            </div>

                            <div className="section p-1">


                                <div className="row mb-3">
                                    <div className="col-md-8">
                                        <p className="obs-title">Description</p>
                                        <p className="obs-content">{reportData.description || ''}</p>
                                    </div>

                                </div>


                                {reportData.permitType !== 'CA' && <div className="row mb-3">
                                    <div className="col-md-12">
                                        <p className="obs-title">Uploaded Documents</p>

                                        {reportData.uploads && reportData.uploads.length > 0 ? (
                                            <div>

                                                {reportData.uploads.filter(item => isImage(item.src)).length > 0 && (
                                                    <GalleryPage photos={reportData.uploads.filter(item => isImage(item.src))} />
                                                )}
                                                {reportData.uploads
                                                    .filter(item => !isImage(item.src))
                                                    .map((item, index) => (
                                                        <p key={index} className="obs-content">
                                                            <a href={item.src} target="_blank" rel="noopener noreferrer">
                                                                {item.src.split('/').pop()}
                                                            </a>
                                                        </p>
                                                    ))
                                                }
                                            </div>
                                        ) : (
                                            <p className="obs-content">No Documents Upload</p>
                                        )}
                                    </div>
                                </div>
                                }


                            </div>

                            <div className="section p-1">
                                {/* Revoke Comments Section, conditionally rendered */}
                                {reportData.status === 'Revoked' && (
                                    <div className="row mb-3">
                                        <div className="col-md-6">
                                            <p className="obs-title">Revoke Comments</p>
                                            <p className="obs-content">{dcop?.suspendComments || 'No comments'}</p>
                                        </div>

                                        <div className="col-md-6">
                                            <p className="obs-title">Applied by</p>
                                            <p className="obs-content">{dcop?.applicantName || 'Not specified'}</p>
                                            <p className="obs-content">{dcop?.company || 'Not specified'}</p>
                                        </div>

                                    </div>
                                )}


                                {/* Reference MOP Title Section */}
                                {reportData.permitType !== 'CA' && <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Reference MOP Title</p>
                                        <p className="obs-content">{dcop?.mopTitle || 'Not specified'}</p>
                                    </div>

                                    <div className="col-md-6">
                                        <p className="obs-title">Change Ticket No</p>
                                        <p className="obs-content">{dcop?.ticketNo || 'Not specified'}</p>
                                    </div>
                                </div>}

                                {/* Change Ticket No Section */}


                                {/* Applicant Contact Number Section */}
                                {reportData.permitType !== 'CA' && <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Applicant Contact Number</p>
                                        <p className="obs-content">{dcop?.contactNo || 'Not specified'}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Company Contact Number</p>
                                        <p className="obs-content">{dcop?.companyContactNo || 'Not specified'}</p>
                                    </div>
                                </div>}

                                {/* Company Contact Number Section */}
                                <div className="row mb-3">

                                </div>

                                {/* Permit Type and Status Sections */}
                                {/* <div className="row mb-3">
                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Type</p>
                                        <p className="obs-content">{reportData.permitType && reportData.permitType === 'CA' ? 'Construction' : reportData.permitType}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="obs-title">Permit Status</p>
                                        <p className="obs-content">{reportData.status || 'Not specified'}</p>
                                    </div>
                                </div> */}
                            </div>
                            {(reportData.status === "Pending HRA Assessment" || reportData.status === "Pending HRA Approval") && (
                                <>
                                    <div className="section p-1">
                                        {dcop?.isDcFire === 'Yes' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Fire Isolation Location(s)</h4>
                                                    {console.log(fireSystems)}
                                                    {fireSystems.map((item, index) => (
                                                        <div key={index} className=" no-horizontal-margin">
                                                            <div className="location-info">
                                                                <div className="location-title blue-bg">
                                                                    <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                </div>
                                                                <div className="location-title light-bg">
                                                                    <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                </div>
                                                            </div>
                                                            <div className="isolation-info">
                                                                {fireSystems.length !== 0 && item.systems.map((item1, i) => (
                                                                    item1.label && (
                                                                        <div key={i} className="isolation-item">
                                                                            <span className="isolation-question">
                                                                                Is the {item1.label} Isolated?
                                                                                <span className="required-star">*</span>
                                                                            </span>
                                                                            {(
                                                                                <span className={`isolation-answer ${item1.isolated === 1 ? 'green' : item1.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                    {item1.isolated === 1 ? 'Yes' : item1.isolated === 0 ? 'Not isolated yet' : 'Not Applicable'}
                                                                                </span>
                                                                            )}
                                                                            {
                                                                                item1.remarks && <span>
                                                                                    <strong>Remarks: </strong> {item1.remarks}
                                                                                </span>
                                                                            }
                                                                        </div>
                                                                    )
                                                                ))}
                                                            </div>
                                                        </div>
                                                    ))}
                                                    {/* ............. */}
                                                    {/* <div className="mt-3">
                                                        <h4 className="text-header">Fire System Further Details</h4>
                                                        {fireSysMore.map((item, index) => (
                                                            <>
                                                                <div key={index} className="d-flex align-items-center mb-2">
                                                                    <div className="d-flex align-items-center mt-4 mb-4" style={{ cursor: 'pointer' }}>
                                                                        <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                                                                        {item.checked &&
                                                                            <i className="material-icons" style={{ position: 'absolute', color: 'white' }}>check</i>
                                                                        }
                                                                        <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                                                                    </div>

                                                                </div>

                                                                {
                                                                    item.checked && item.attachment && (
                                                                        <div className="ml-3 mb-4">
                                                                            {item.uploads && item.uploads.length > 0 ? (
                                                                                <div>


                                                                                    {item.uploads

                                                                                        .map((item, index) => (
                                                                                            <p key={index} className="obs-content">
                                                                                                <a href={STATIC_URL + '/' + item.url} target="_blank" rel="noopener noreferrer">
                                                                                                    {item.url.split('/').pop()}
                                                                                                </a>
                                                                                            </p>
                                                                                        ))
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <p className="obs-content">No Documents Upload</p>
                                                                            )}
                                                                        </div>
                                                                    )
                                                                }

                                                            </>
                                                        ))}
                                                    </div> */}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcFire === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Fire Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    <div className="section p-1">
                                        {dcop?.isDcSecurity === 'Yes' && (
                                            <div>
                                                <div className="">
                                                    <h4 className="text-header">Security Isolation Location(s)</h4>
                                                    {securitySystems.map((item, index) => {
                                                        console.log(item, 'in')
                                                        if (item.loc5.id !== undefined) {
                                                            return (
                                                                <div key={index} className="  no-horizontal-margin">
                                                                    <div className="location-info">
                                                                        <div className="location-title blue-bg">
                                                                            <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                        </div>
                                                                        <div className="location-title light-bg">
                                                                            <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="isolation-info">
                                                                        {securitySystems.length !== 0 && item.systems.map((system, i) => {
                                                                            return (
                                                                                system.label && (
                                                                                    <div key={i} className="isolation-item">
                                                                                        <span className="isolation-question">
                                                                                            Is the {system.label} Isolated?
                                                                                            <span className="required-star">*</span>
                                                                                        </span>
                                                                                        <span className={`isolation-answer ${system.isolated === 1 ? 'green' : system.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                            {system.isolated === 1 ? 'Yes' : system.isolated === 0 ? 'No' : 'Not Applicable'}
                                                                                        </span>
                                                                                        {
                                                                                            system.remarks && <span>
                                                                                                <strong>Remarks: </strong> {system.remarks}
                                                                                            </span>
                                                                                        }
                                                                                    </div>
                                                                                )
                                                                            );
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        } else {
                                                            return (
                                                                <div key={index} className="isolation-item na">
                                                                    <span>N/A</span>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcSecurity === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Security Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}

                                    </div>
                                    {/* ....................... */}
                                    {/* <div className="section p-1">
                                        {dcop?.isDcEnergy === 'Yes' && (
                                            <div>
                                                <div className="">
                                                    <h4 className="text-header">Energy Isolation Location(s)</h4>
                                                    {energySystems.map((item, index) => {
                                                        console.log(item, 'in')
                                                        if (item.loc5.id !== undefined) {
                                                            return (
                                                                <div key={index} className="  no-horizontal-margin">
                                                                    <div className="location-info">
                                                                        <div className="location-title blue-bg">
                                                                            <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                        </div>
                                                                        <div className="location-title light-bg">
                                                                            <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="isolation-info">
                                                                        {energySystems.length !== 0 && item.systems.map((system, i) => {
                                                                            return (
                                                                                system.label && (<>
                                                                                    <div key={i} className="isolation-item">
                                                                                        <span className="isolation-question">
                                                                                            Is the {system.label} Isolated?
                                                                                            <span className="required-star">*</span>
                                                                                        </span>
                                                                                        <span className={`isolation-answer ${system.isolated === 1 ? 'green' : system.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                            {system.isolated === 1 ? 'Yes' : system.isolated === 0 ? 'No' : 'Not Applicable'}
                                                                                        </span>
                                                                                        {
                                                                                            system.remarks && <span>
                                                                                                <strong>Remarks: </strong> {system.remarks}
                                                                                            </span>
                                                                                        }
                                                                                    </div>


                                                                                </>)
                                                                            );
                                                                        })}
                                                                    </div>
                                                                    <div className="mt-3 mb-3">
                                                                        {item.systemName && item.systemName.map((tag) => {
                                                                            return (<div className="isolation-item">
                                                                                <span className="isolation-question">
                                                                                    Name/ID of the system(s) / device(s)
                                                                                </span>
                                                                                <span className="isolation-answer">
                                                                                    {tag}
                                                                                </span>
                                                                            </div>)
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        } else {
                                                            return (
                                                                <div key={index} className="isolation-item na">
                                                                    <span>N/A</span>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcEnergy === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Energy Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}

                                    </div> */}
                                </>
                            )}


                            {reportData.status !== "Pending DCSO Isolation / Acknowledgement" && reportData.status !== "Pending HRA Assessment" && applcationDetail === '' ?
                                <>
                                    <div className="section p-1">
                                        {dcop?.isDcFire === 'Yes' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Fire Isolation Location(s)</h4>
                                                    {console.log(fireSystems)}
                                                    {fireSystems.map((item, index) => (
                                                        <div key={index} className=" no-horizontal-margin">
                                                            <div className="location-info">
                                                                <div className="location-title blue-bg">
                                                                    <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                </div>
                                                                <div className="location-title light-bg">
                                                                    <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                </div>
                                                            </div>
                                                            <div className="isolation-info">
                                                                {fireSystems.length !== 0 && item.systems.map((item1, i) => (
                                                                    item1.label && (
                                                                        <div key={i} className="isolation-item">
                                                                            <span className="isolation-question">
                                                                                Is the {item1.label} Isolated?
                                                                                <span className="required-star">*</span>
                                                                            </span>
                                                                            {(
                                                                                <span className={`isolation-answer ${item1.isolated === 1 ? 'green' : item1.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                    {item1.isolated === 1 ? 'Yes' : item1.isolated === 0 ? 'Not isolated yet' : 'Not Applicable'}
                                                                                </span>
                                                                            )}
                                                                            {
                                                                                item1.remarks && <span>
                                                                                    <strong>Remarks: </strong> {item1.remarks}
                                                                                </span>
                                                                            }
                                                                        </div>
                                                                    )
                                                                ))}
                                                            </div>
                                                        </div>
                                                    ))}

                                                    {/* ....................... */}
                                                    {/* <div className="mt-3">
                                                        <h4 className="text-header">Fire System Further Details</h4>
                                                        {fireSysMore.map((item, index) => (
                                                            <>
                                                                <div key={index} className="d-flex align-items-center mb-2">
                                                                    <div className="d-flex align-items-center mt-4 mb-4" style={{ cursor: 'pointer' }}>
                                                                        <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                                                                        {item.checked &&
                                                                            <i className="material-icons" style={{ position: 'absolute', color: 'white' }}>check</i>
                                                                        }
                                                                        <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                                                                    </div>

                                                                </div>

                                                                {
                                                                    item.checked && item.attachment && (
                                                                        <div className="ml-3 mb-4">
                                                                            {item.uploads && item.uploads.length > 0 ? (
                                                                                <div>


                                                                                    {item.uploads

                                                                                        .map((item, index) => (
                                                                                            <p key={index} className="obs-content">
                                                                                                <a href={STATIC_URL + '/' + item.url} target="_blank" rel="noopener noreferrer">
                                                                                                    {item.url.split('/').pop()}
                                                                                                </a>
                                                                                            </p>
                                                                                        ))
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <p className="obs-content">No Documents Upload</p>
                                                                            )}
                                                                        </div>
                                                                    )
                                                                }

                                                            </>
                                                        ))}
                                                    </div> */}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcFire === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Fire Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    <div className="section p-1">
                                        {dcop?.isDcSecurity === 'Yes' && (
                                            <div>
                                                <div className="">
                                                    <h4 className="text-header">Security Isolation Location(s)</h4>
                                                    {securitySystems.map((item, index) => {
                                                        console.log(item, 'in')
                                                        if (item.loc5.id !== undefined) {
                                                            return (
                                                                <div key={index} className="  no-horizontal-margin">
                                                                    <div className="location-info">
                                                                        <div className="location-title blue-bg">
                                                                            <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                        </div>
                                                                        <div className="location-title light-bg">
                                                                            <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="isolation-info">
                                                                        {securitySystems.length !== 0 && item.systems.map((system, i) => {
                                                                            return (
                                                                                system.label && (
                                                                                    <div key={i} className="isolation-item">
                                                                                        <span className="isolation-question">
                                                                                            Is the {system.label} Isolated?
                                                                                            <span className="required-star">*</span>
                                                                                        </span>
                                                                                        <span className={`isolation-answer ${system.isolated === 1 ? 'green' : system.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                            {system.isolated === 1 ? 'Yes' : system.isolated === 0 ? 'No' : 'Not Applicable'}
                                                                                        </span>
                                                                                        {
                                                                                            system.remarks && <span>
                                                                                                <strong>Remarks: </strong> {system.remarks}
                                                                                            </span>
                                                                                        }
                                                                                    </div>
                                                                                )
                                                                            );
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        } else {
                                                            return (
                                                                <div key={index} className="isolation-item na">
                                                                    <span>N/A</span>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcSecurity === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Security Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}

                                    </div>
                                    {/* .................... */}
                                    {/* <div className="section p-1">
                                        {dcop?.isDcEnergy === 'Yes' && (
                                            <div>
                                                <div className="">
                                                    <h4 className="text-header">Energy Isolation Location(s)</h4>
                                                    {energySystems.map((item, index) => {
                                                        console.log(item, 'in')
                                                        if (item.loc5.id !== undefined) {
                                                            return (
                                                                <div key={index} className="  no-horizontal-margin">
                                                                    <div className="location-info">
                                                                        <div className="location-title blue-bg">
                                                                            <span>{item.loc5.loc5Title} - {item.loc5.name}</span>
                                                                        </div>
                                                                        <div className="location-title light-bg">
                                                                            <span>{item.loc6.loc6Title} - {item.loc6.name}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="isolation-info">
                                                                        {energySystems.length !== 0 && item.systems.map((system, i) => {
                                                                            return (
                                                                                system.label && (<>
                                                                                    <div key={i} className="isolation-item">
                                                                                        <span className="isolation-question">
                                                                                            Is the {system.label} Isolated?
                                                                                            <span className="required-star">*</span>
                                                                                        </span>
                                                                                        <span className={`isolation-answer ${system.isolated === 1 ? 'green' : system.isolated === 0 ? 'red' : 'orange'}`}>
                                                                                            {system.isolated === 1 ? 'Yes' : system.isolated === 0 ? 'No' : 'Not Applicable'}
                                                                                        </span>
                                                                                        {
                                                                                            system.remarks && <span>
                                                                                                <strong>Remarks: </strong> {system.remarks}
                                                                                            </span>
                                                                                        }
                                                                                    </div>


                                                                                </>)
                                                                            );
                                                                        })}
                                                                    </div>
                                                                    <div className="mt-3 mb-3">
                                                                        {item.systemName && item.systemName.map((tag) => {
                                                                            return (<div className="isolation-item">
                                                                                <span className="isolation-question">
                                                                                    Name/ID of the system(s) / device(s)
                                                                                </span>
                                                                                <span className="isolation-answer">
                                                                                    {tag}
                                                                                </span>
                                                                            </div>)
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        } else {
                                                            return (
                                                                <div key={index} className="isolation-item na">
                                                                    <span>N/A</span>
                                                                </div>
                                                            );
                                                        }
                                                    })}
                                                </div>
                                            </div>
                                        )}

                                        {dcop?.isDcEnergy === 'No' && (
                                            <div>
                                                <div className=" ">
                                                    <h4 className="text-header">Energy Isolation Location(s)</h4>
                                                    <p>No</p>
                                                </div>
                                            </div>
                                        )}

                                    </div> */}
                                </> :
                                <>
                                {/* .................. */}
                                    {/* {reportData.status === "Pending DCSO Isolation / Acknowledgement" && (
                                        <>
                                            <div className="section p-1">

                                                <div>
                                                    <div className=" ">
                                                        <h4 className="text-header">Fire Isolation Location(s)</h4>
                                                        {dcop?.isDcFire === 'Yes' && (<>
                                                            {fireSystems.map((item, index) => (

                                                                <div key={index} className=" no-horizontal-margin boxShadow p-3 mb-3" style={{ position: 'relative' }}>
                                                                    <div className="location-info">
                                                                        <div className="location-title blue-bg">
                                                                            <span>{title[4]?.altTitle} - {item.loc5.name}</span>
                                                                        </div>
                                                                        <div className="location-title light-bg">
                                                                            <span>{title[5]?.altTitle} - {item.loc6.name}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="isolation-info">
                                                                        {fireSystems.length !== 0 && item.systems.map((item1, i) => (
                                                                            item1.label && (
                                                                                <div key={i} className="isolation-item">
                                                                                    <span className="isolation-question">
                                                                                        Is the {item1.label} Isolated?
                                                                                        <span className="required-star">*</span>
                                                                                    </span>
                                                                                    {item.isNew ?
                                                                                        <span>Yes</span>

                                                                                        : <>
                                                                                            <div className="isolation-options p-3 d-flex">
                                                                                                <Button
                                                                                                    className="me-3"
                                                                                                    style={{
                                                                                                        backgroundColor: item1.isolated === 1 ? '#005284' : '#f8f9fa',
                                                                                                        color: item1.isolated === 1 ? 'white' : '#000000',
                                                                                                        border: '1px solid #dee2e6',
                                                                                                        borderRadius: '10px'
                                                                                                    }}
                                                                                                    onClick={() => handleIsolationChange(index, i, 1)}
                                                                                                >
                                                                                                    Yes
                                                                                                </Button>
                                                                                                <Button
                                                                                                    style={{
                                                                                                        backgroundColor: item1.isolated === -1 ? '#005284' : '#f8f9fa',
                                                                                                        color: item1.isolated === -1 ? 'white' : '#000000',
                                                                                                        border: '1px solid #dee2e6',
                                                                                                        borderRadius: '10px'
                                                                                                    }}
                                                                                                    onClick={() => handleIsolationChange(index, i, -1)}
                                                                                                >
                                                                                                    Not Applicable
                                                                                                </Button>
                                                                                            </div>
                                                                                            {item1.isolated === -1 && (
                                                                                                <div className="remarks-input">
                                                                                                    <label>
                                                                                                        Remarks:
                                                                                                        <input
                                                                                                            type="text"
                                                                                                            className="form-control"
                                                                                                            value={item1.remarks}
                                                                                                            onChange={(e) =>
                                                                                                                handleRemarksChange(index, i, e.target.value)
                                                                                                            }
                                                                                                            placeholder="Enter remarks"
                                                                                                        />
                                                                                                    </label>
                                                                                                </div>
                                                                                            )}
                                                                                        </>}
                                                                                </div>
                                                                            )
                                                                        ))}
                                                                    </div>

                                                                    {item.isNew && (
                                                                        <i
                                                                            className="material-icons close-icon"
                                                                            style={{ color: 'red', cursor: 'pointer', position: 'absolute', top: 5, right: 10 }}
                                                                            onClick={() => handleFSRemoveSystem(index)}
                                                                        >
                                                                            close
                                                                        </i>
                                                                    )}
                                                                </div>
                                                            ))}


                                                        </>)}
                                                    </div>
                                                </div>



                                                <div className="row">
                                                    <div className="col-12">
                                                        <p className="mt-2">Perform additional FireIsolations, if Considered necessary</p>

                                                        <Button className="primary" onClick={() => { setIsoModal(true); setFS(fire) }}>Add</Button>
                                                    </div>
                                                </div>

                                                <div className="section p-1">
                                                    <div className="row">
                                                        <div className="col-12">
                                                            <label>Is there any need to inform for fire department? </label>
                                                            <div className="isolation-options p-3 d-flex">
                                                                <Button
                                                                    className="me-3"
                                                                    style={{
                                                                        backgroundColor: informFireDept === 'Yes' ? '#005284' : '#f8f9fa',
                                                                        color: informFireDept === 'Yes' ? 'white' : '#000000',
                                                                        border: '1px solid #dee2e6',
                                                                        borderRadius: '10px'
                                                                    }}
                                                                    onClick={() => handleFireDeptChange('Yes')}
                                                                >
                                                                    Yes
                                                                </Button>
                                                                <Button
                                                                    style={{
                                                                        backgroundColor: informFireDept === 'No' ? '#005284' : '#f8f9fa',
                                                                        color: informFireDept === 'No' ? 'white' : '#000000',
                                                                        border: '1px solid #dee2e6',
                                                                        borderRadius: '10px'
                                                                    }}
                                                                    onClick={() => handleFireDeptChange('No')}
                                                                >
                                                                    No
                                                                </Button>
                                                            </div>

                                                           
                                                            {informFireDept === 'Yes' && (
                                                                <div className="mt-3">
                                                                    <Form.Group>
                                                                        <Form.Label>Please provide fire department details:</Form.Label>
                                                                        <Form.Control
                                                                            as="textarea"
                                                                            rows={3}
                                                                            value={fireDeptDetails}
                                                                            onChange={(e) => setFireDeptDetails(e.target.value)}
                                                                            placeholder="Enter fire department details"
                                                                        />
                                                                    </Form.Group>
                                                                </div>
                                                            )}
                                                        </div>

                                                        <div className="col-12 mt-4">
                                                            <label>Is there any need to inform for fire insurance company? </label>
                                                            <div className="isolation-options p-3 d-flex">
                                                                <Button
                                                                    className="me-3"
                                                                    style={{
                                                                        backgroundColor: informFireInsurance === 'Yes' ? '#005284' : '#f8f9fa',
                                                                        color: informFireInsurance === 'Yes' ? 'white' : '#000000',
                                                                        border: '1px solid #dee2e6',
                                                                        borderRadius: '10px'
                                                                    }}
                                                                    onClick={() => handleFireInsuranceChange('Yes')}
                                                                >
                                                                    Yes
                                                                </Button>
                                                                <Button
                                                                    style={{
                                                                        backgroundColor: informFireInsurance === 'No' ? '#005284' : '#f8f9fa',
                                                                        color: informFireInsurance === 'No' ? 'white' : '#000000',
                                                                        border: '1px solid #dee2e6',
                                                                        borderRadius: '10px'
                                                                    }}
                                                                    onClick={() => handleFireInsuranceChange('No')}
                                                                >
                                                                    No
                                                                </Button>
                                                            </div>

                                                          
                                                            {informFireInsurance === 'Yes' && (
                                                                <div className="mt-3">
                                                                    <Form.Group>
                                                                        <Form.Label>Please provide fire insurance company details:</Form.Label>
                                                                        <Form.Control
                                                                            as="textarea"
                                                                            rows={3}
                                                                            value={fireInsuranceDetails}
                                                                            onChange={(e) => setFireInsuranceDetails(e.target.value)}
                                                                            placeholder="Enter fire insurance company details"
                                                                        />
                                                                    </Form.Group>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mt-3">
                                                    <h4 className="text-header">Fire System Further Details</h4>
                                                    {fireSysMore.map((item, index) => (
                                                        <>
                                                            <div key={index} className="d-flex align-items-center mb-2">
                                                                <div className="d-flex align-items-center mt-4 mb-4" style={{ cursor: 'pointer' }}>
                                                                    <Container style={{ width: 24, height: 24, borderRadius: 12, background: item.checked ? '#005284' : 'lightgray', cursor: 'pointer' }} />
                                                                    {item.checked &&
                                                                        <i className="material-icons" style={{ position: 'absolute', color: 'white' }}>check</i>
                                                                    }
                                                                    <label style={{ marginLeft: '8px', cursor: 'pointer' }}>{item.label}</label>
                                                                </div>

                                                            </div>

                                                            {
                                                                item.checked && item.attachment && (
                                                                    <div className="ml-3 mb-4">
                                                                        {item.uploads && item.uploads.length > 0 ? (
                                                                            <div>


                                                                                {item.uploads

                                                                                    .map((item, index) => (
                                                                                        <p key={index} className="obs-content">
                                                                                            <a href={STATIC_URL + '/' + item.url} target="_blank" rel="noopener noreferrer">
                                                                                                {item.url.split('/').pop()}
                                                                                            </a>
                                                                                        </p>
                                                                                    ))
                                                                                }
                                                                            </div>
                                                                        ) : (
                                                                            <p className="obs-content">No Documents Upload</p>
                                                                        )}
                                                                    </div>
                                                                )
                                                            }

                                                        </>
                                                    ))}
                                                </div>
                                            </div>
                                            <div className="section p-1">

                                                <div>
                                                    <h4 className="text-header">Security Isolation Location(s)</h4>
                                                    {dcop?.isDcSecurity === 'Yes' && (<>
                                                        {
                                                            securitySystems.map((item, systemIndex) => {
                                                                if (item.loc5.id !== undefined) {
                                                                    return (
                                                                        <div key={systemIndex} className="no-horizontal-margin boxShadow p-3 mb-3" style={{ position: 'relative' }}>
                                                                            <div className="location-info">
                                                                                <div className="location-title blue-bg">
                                                                                    <span>{title[4]?.altTitle} - {item.loc5.name}</span>
                                                                                </div>
                                                                                <div className="location-title light-bg">
                                                                                    <span>{title[5]?.altTitle} - {item.loc6.name}</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="isolation-info">
                                                                                {item.systems.map((system, itemIndex) => (
                                                                                    system.label && (
                                                                                        <div key={itemIndex} className="isolation-item">
                                                                                            <span className="isolation-question">
                                                                                                Is the {system.label} Isolated?
                                                                                                <span className="required-star">*</span>
                                                                                            </span>

                                                                                            {item.isNew ?
                                                                                                <span>Yes</span>

                                                                                                : <>
                                                                                                    <div className="isolation-options p-3 d-flex">
                                                                                                        <Button
                                                                                                            className="me-3"
                                                                                                            style={{
                                                                                                                backgroundColor: system.isolated === 1 ? '#005284' : '#f8f9fa',
                                                                                                                color: system.isolated === 1 ? 'white' : '#000000',
                                                                                                                border: '1px solid #dee2e6',
                                                                                                                borderRadius: '10px'
                                                                                                            }}
                                                                                                            onClick={() => handleIsolationSecurityChange(systemIndex, itemIndex, 1)}
                                                                                                        >
                                                                                                            Yes
                                                                                                        </Button>
                                                                                                        <Button
                                                                                                            style={{
                                                                                                                backgroundColor: system.isolated === -1 ? '#005284' : '#f8f9fa',
                                                                                                                color: system.isolated === -1 ? 'white' : '#000000',
                                                                                                                border: '1px solid #dee2e6',
                                                                                                                borderRadius: '10px'
                                                                                                            }}
                                                                                                            onClick={() => handleIsolationSecurityChange(systemIndex, itemIndex, -1)}
                                                                                                        >
                                                                                                            Not Applicable
                                                                                                        </Button>
                                                                                                    </div>
                                                                                                    {system.isolated === -1 && (
                                                                                                        <div className="remarks-input">
                                                                                                            <label>
                                                                                                                Remarks:
                                                                                                                <input
                                                                                                                    type="text"
                                                                                                                    className="form-control"
                                                                                                                    value={system.remarks}
                                                                                                                    onChange={(e) =>
                                                                                                                        handleSecurityRemarksChange(systemIndex, itemIndex, e.target.value)
                                                                                                                    }
                                                                                                                    placeholder="Enter remarks"
                                                                                                                />
                                                                                                            </label>
                                                                                                        </div>
                                                                                                    )}
                                                                                                </>}
                                                                                        </div>
                                                                                    )
                                                                                ))}
                                                                            </div>
                                                                            {item.isNew && (
                                                                                <i
                                                                                    className="material-icons close-icon"
                                                                                    style={{ color: 'red', cursor: 'pointer', position: 'absolute', top: 5, right: 10 }}
                                                                                    onClick={() => handleRemoveSystem(systemIndex)}
                                                                                >
                                                                                    close
                                                                                </i>
                                                                            )}
                                                                        </div>
                                                                    );
                                                                } else {
                                                                    return (
                                                                        <div key={systemIndex} className="isolation-item na">
                                                                            <span>N/A</span>
                                                                        </div>
                                                                    );
                                                                }
                                                            })
                                                        }
                                                    </>)}

                                                </div>

                                                <div className="row">
                                                    <div className="col-12">
                                                        <p className="mt-2">Perform additional Security Isolations, if Considered necessary</p>

                                                        <Button className="primary" onClick={() => { setIsoSSModal(true); setSS(security) }}>Add</Button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="section p-1">

                                                <div>
                                                    <h4 className="text-header">Energy Isolation Location(s)</h4>
                                                    {dcop?.isDcEnergy === 'Yes' && (<>
                                                        {
                                                            energySystems.map((item, systemIndex) => {
                                                                if (item.loc5.id !== undefined) {
                                                                    return (<>
                                                                        <div key={systemIndex} className="no-horizontal-margin boxShadow p-3 mb-3" style={{ position: 'relative' }}>
                                                                            <div className="location-info">
                                                                                <div className="location-title blue-bg">
                                                                                    <span>{title[4]?.altTitle} - {item.loc5.name}</span>
                                                                                </div>
                                                                                <div className="location-title light-bg">
                                                                                    <span>{title[5]?.altTitle} - {item.loc6.name}</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="isolation-info">
                                                                                {item.systems.map((system, itemIndex) => (
                                                                                    system.label && (
                                                                                        <div key={itemIndex} className="isolation-item">
                                                                                            <span className="isolation-question">
                                                                                                Is the {system.label} Isolated?
                                                                                                <span className="required-star">*</span>
                                                                                            </span>

                                                                                            {item.isNew ?
                                                                                                <span>Yes</span>

                                                                                                : <>
                                                                                                    <div className="isolation-options p-3 d-flex">
                                                                                                        <Button
                                                                                                            className="me-3"
                                                                                                            style={{
                                                                                                                backgroundColor: system.isolated === 1 ? '#005284' : '#f8f9fa',
                                                                                                                color: system.isolated === 1 ? 'white' : '#000000',
                                                                                                                border: '1px solid #dee2e6',
                                                                                                                borderRadius: '10px'
                                                                                                            }}
                                                                                                            onClick={() => handleIsolationEnergyChange(systemIndex, itemIndex, 1)}
                                                                                                        >
                                                                                                            Yes
                                                                                                        </Button>
                                                                                                        <Button
                                                                                                            style={{
                                                                                                                backgroundColor: system.isolated === -1 ? '#005284' : '#f8f9fa',
                                                                                                                color: system.isolated === -1 ? 'white' : '#000000',
                                                                                                                border: '1px solid #dee2e6',
                                                                                                                borderRadius: '10px'
                                                                                                            }}
                                                                                                            onClick={() => handleIsolationEnergyChange(systemIndex, itemIndex, -1)}
                                                                                                        >
                                                                                                            Not Applicable
                                                                                                        </Button>
                                                                                                    </div>
                                                                                                    {system.isolated === -1 && (
                                                                                                        <div className="remarks-input">
                                                                                                            <label>
                                                                                                                Remarks:
                                                                                                                <input
                                                                                                                    type="text"
                                                                                                                    className="form-control"
                                                                                                                    value={system.remarks}
                                                                                                                    onChange={(e) =>
                                                                                                                        handleSecurityRemarksChange(systemIndex, itemIndex, e.target.value)
                                                                                                                    }
                                                                                                                    placeholder="Enter remarks"
                                                                                                                />
                                                                                                            </label>
                                                                                                        </div>
                                                                                                    )}
                                                                                                </>}
                                                                                        </div>
                                                                                    )
                                                                                ))}
                                                                            </div>
                                                                            {item.isNew && (
                                                                                <i
                                                                                    className="material-icons close-icon"
                                                                                    style={{ color: 'red', cursor: 'pointer', position: 'absolute', top: 5, right: 10 }}
                                                                                    onClick={() => handleRemoveSystem(systemIndex)}
                                                                                >
                                                                                    close
                                                                                </i>
                                                                            )}
                                                                        </div>

                                                                        <div className="mt-3 mb-3">
                                                                            {item.systemName && item.systemName.map((tag) => {
                                                                                return (<div className="isolation-item">
                                                                                    <span className="isolation-question">
                                                                                        Name/ID of the system(s) / device(s)
                                                                                    </span>
                                                                                    <span className="isolation-answer">
                                                                                        {tag}
                                                                                    </span>
                                                                                </div>)
                                                                            })}
                                                                        </div>
                                                                    </>);
                                                                } else {
                                                                    return (
                                                                        <div key={systemIndex} className="isolation-item na">
                                                                            <span>N/A</span>
                                                                        </div>
                                                                    );
                                                                }
                                                            })
                                                        }
                                                    </>)}

                                                </div>

                                                <div className="row">
                                                    <div className="col-12">
                                                        <p className="mt-2">Perform additional Energy Isolations, if Considered necessary</p>

                                                        <Button className="primary" onClick={() => { setIsoEnergyModal(true); setES(energySysLocations) }}>Add</Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    )} */}
                                </>
                            }
                            <div className="section p-1">
                                {dcop?.highRisk === 'Yes' && (
                                    <div>
                                        <div className="">
                                            <h4 className="text-header">High Risk activities involved in this work:</h4>

                                            {permits.map((item, index) => {
                                                if (item.checked === 'Yes') {
                                                    return (
                                                        <div key={index}>
                                                            <p className="list-item">{index + 1}. {item.label}</p>
                                                        </div>
                                                    );
                                                }
                                                return null;
                                            })}
                                        </div>

                                        {permits.some(data => data.checked === 'Yes') &&
                                            <div>
                                                <div className="  sso-background">
                                                    <h4 className="safety-checklist-header">Safety Checklist</h4>
                                                    <p className="safety-checklist-subheader">*Physical Verification of Compliance Required</p>
                                                </div>
                                                <div className="row">
                                                    {permitChecklist.map((item, index) => {
                                                        const hasMatch = permits.some(data => item.applicable.includes((data.id - 1)) && data.checked === 'Yes');
                                                        if (hasMatch) {
                                                            return (
                                                                <div className="col-4">
                                                                    <div key={index} className=" box">
                                                                        <p className="checklist-item">{index + 1}. {item.label}</p>
                                                                        {permits.map((data, i) => {
                                                                            if (item.applicable.includes(data.id - 1) && data.checked === 'Yes') {
                                                                                return (
                                                                                    <div key={i} className="activity-confirmation col-6">
                                                                                        <p className="activity-question">Confirmed for {data.label} Activity?</p>
                                                                                        <p className="activity-answer">{getConfirmationLabel(data, item.id)}</p>
                                                                                        {checklists?.[item.id]?.[data.id]?.personnel && (
                                                                                            <p className="activity-answer">
                                                                                                <strong>Name of the Person who inspected: </strong>
                                                                                                {getPersonnel(data, item.id)}
                                                                                            </p>
                                                                                        )}
                                                                                        {checklists?.[item.id]?.[data.id]?.remarks && (
                                                                                            <p className="activity-answer">
                                                                                                <strong>Remarks: </strong>
                                                                                                {getRemarks(data, item.id)}
                                                                                            </p>
                                                                                        )}
                                                                                    </div>
                                                                                );
                                                                            }
                                                                            return null;
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        }
                                                        return null;
                                                    })}
                                                </div>
                                            </div>
                                        }
                                    </div>
                                )}

                                {dcop?.highRisk === 'No' && (<>
                                    <h4 className="text-header">High Risk activities involved in this work</h4>
                                    <p>No</p>
                                </>)}

                            </div>

                            <div className="section p-1">
                                {console.log(reportData.permitType, 'type')}
                                {reportData.permitType === 'CA' && (
                                    <div>
                                        <div className=" ">
                                            <h4 className="text-header">High Risk activities involved in this work:</h4>

                                            {permits.map((item, index) => {
                                                if (item.checked === 'Yes') {
                                                    return (
                                                        <div key={index}>
                                                            <p className="list-item">{index + 1}. {item.label}</p>
                                                        </div>
                                                    );
                                                }
                                                return null;
                                            })}
                                        </div>

                                        {permits.some(data => data.checked === 'Yes') &&
                                            <div>
                                                <div className="  sso-background">
                                                    <h4 className="safety-checklist-header">Safety Checklist</h4>
                                                    <p className="safety-checklist-subheader">*Physical Verification of Compliance Required</p>
                                                </div>
                                                <div className="row p-3">
                                                    {permitChecklist.map((item, index) => {
                                                        const hasMatch = permits.some(data => item.applicable.includes((data.id - 1)) && data.checked === 'Yes');
                                                        if (hasMatch) {
                                                            return (
                                                                <div className="col-4">
                                                                    <div key={index} className=" box">
                                                                        <p className="checklist-item">{index + 1}. {item.label}</p>
                                                                        {permits.map((data, i) => {
                                                                            if (item.applicable.includes(data.id - 1) && data.checked === 'Yes') {
                                                                                return (
                                                                                    <div key={i} className="activity-confirmation">
                                                                                        <p className="activity-question">Confirmed for {data.label} Activity?</p>
                                                                                        <p className="activity-answer">{getConfirmationLabel(data, item.id)}</p>
                                                                                    </div>
                                                                                );
                                                                            }
                                                                            return null;
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            );
                                                        }
                                                        return null;
                                                    })}
                                                </div>
                                            </div>
                                        }
                                    </div>
                                )}


                            </div>

                            <div className="section p-1 row">
                                {(dcop && reportData.permitType !== 'CA') && (<>
                                    <div className="col-6">
                                        <h4 className="text-header mt-3">Applicant</h4>
                                        <p style={{ textAlign: 'justify' }}> I confirm that I am the supervisor in charge of the activity(ies)
                                            and am suitably competent to carry out the Person-In-Charge
                                            function. I have read and fully understand the SWMS/SWP and
                                            all the safety precautions to be taken under current legislations
                                            and STT GDC's Group Minimum Standards. I confirm all
                                            SWMS/SWP & Safety Checklist Conditions are complied with.
                                            To the best of my knowledge, this activity is safe to proceed.</p>
                                        <div>
                                            {dcop.applicantSign && (
                                                <img src={applicantSign} alt="Applicant Signature" style={{ width: '200px' }} />
                                            )}
                                            <p className="info">{dcop.applicantName}</p>
                                            <p className="info">Signed on: {dcop.applicantSignedDate}</p>
                                        </div>
                                    </div>
                                    {dcop.approverSign && (
                                        <div className="col-6">
                                            <h4 className="text-header mt-3">Approver</h4>
                                            <p style={{ textAlign: 'justify' }}>I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).</p>
                                            <div>
                                                {dcop.approverSign && (
                                                    <img src={approverSign} alt="Applicant Signature" style={{ width: '200px' }} />
                                                )}
                                                <p className="info">{dcop.approverName
                                                }</p>
                                                <p className="info">Signed on: {dcop.approverSignedDate}</p>
                                                <div className="">
                                                    <h4 className="text-header ">Approver Comments</h4>
                                                    <p className="info">{dcop.approverComments || 'No comments'}</p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </>)}

                                {dcop && dcop.highRisk === 'Yes' && (<>

                                    <div className="col-6">

                                        {reportData.high_risk && reportData.high_risk.assessorSign && <>


                                            {reportData.status.includes('Active') || reportData.status.includes('Approval') || reportData.status === 'Rejected' || reportData.status === 'Revoked' || reportData.status.includes("Closed") || (reportData.status.includes('Pending DCSO Isolation / Acknowledgement')) ? (
                                                <div className="">
                                                    <h4 className="text-header mt-3">Assessor</h4>
                                                    <p style={{ textAlign: 'justify' }}>I confirm that I hold the appropriate qualification and
                                                        competence to assess this permit. I have reviewed all
                                                        aspects of the task(s)/activity(ies) and confirm that all
                                                        SWMS/SWP & Safety Checklist conditions are complied
                                                        with. To the best of my knowledge, this activity is safe to
                                                        proceed.</p>
                                                    <div>
                                                        {reportData.high_risk?.assessorSign && (
                                                            <img src={assessorSign} alt="Assessor Signature" style={{ width: '200px' }} />
                                                        )}
                                                        <p className="info">{reportData.high_risk?.assessorName}</p>
                                                        <p className="info">Signed on: {reportData.high_risk?.assessorSignedDate}</p>

                                                        {((reportData.status.includes('Active') || reportData.status.includes('Approval') || reportData.status === 'Rejected')) || (reportData.status.includes('Approval')) || (reportData.status.includes('Pending DCSO Isolation / Acknowledgement')) ? (
                                                            <div className="">
                                                                <h4 className="text-header ">Assessor Comments</h4>
                                                                <p className="info">{reportData.high_risk?.assessorComments || 'No comments'}</p>
                                                            </div>
                                                        ) : null}
                                                    </div>
                                                </div>
                                            ) : null}

                                        </>}
                                    </div>
                                    <div className="col-6">


                                        {reportData.status.includes('Active') || reportData.status.includes('DCSO') || reportData.status === 'Rejected' || reportData.status === 'Revoked' || reportData.status.includes("Closed") || reportData.status.includes('Closed & Pending') || reportData.status.includes('Pending DCSO Isolation') ? (
                                            <div className="">
                                                <h4 className="text-header  mt-3">Approver</h4>
                                                <p style={{ textAlign: 'justify' }}> I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).</p>

                                                <div>
                                                    {reportData.high_risk?.approverSign && (
                                                        <img src={approverSign} alt="Approver Signature" style={{ width: '200px' }} />
                                                    )}
                                                    <p className="info">{reportData.high_risk?.approverName}</p>
                                                    <p className="info">Signed on: {reportData.high_risk?.approverSignedDate}</p>

                                                    {((reportData.status.includes('Active') || reportData.status === 'Rejected' || reportData.status.includes('Closed & Pending'))) || ((reportData.status.includes('DCSO') || reportData.status.includes('Closed & Pending'))) ? (
                                                        <div className="">
                                                            <h4 className="text-header ">Approver Comments</h4>
                                                            <p className="info">{reportData.high_risk?.approverComments || 'No comments'}</p>
                                                        </div>
                                                    ) : null}
                                                </div>
                                            </div>
                                        ) : null}
                                    </div>
                                </>

                                )}


                                {(dcop && dcop?.dcsoName) &&
                                    <div className="col-6 ">


                                        {reportData.status.includes('Active') || reportData.status === 'Rejected' || reportData.status === 'Revoked' || reportData.status.includes('Closed & Pending') || reportData.status.includes("Closed") ? (
                                            <div className="">
                                                <h4 className="text-header  mt-3">DCSO Representative</h4>
                                                <p style={{ textAlign: 'justify' }}>I acknowledge this permit application. I confirm that where
                                                    applicable, work area hazards have been communicated,
                                                    isolation has been completed and made safe in
                                                    compliance with current legislations and STT GDC's
                                                    requirements. To the best of my knowledge, the work area
                                                    is handed over to the relevant party in a safe condition.</p>

                                                <div>
                                                    {dcop.dcsoSign && (
                                                        <img src={dcsoSign} alt="DCSO Signature" style={{ width: '200px' }} />
                                                    )}
                                                    <p className="info">{dcop?.dcsoName}</p>
                                                    {dcop.dcsoSignedDate && <p className="info">Signed on: {dcop.dcsoSignedDate}</p>}
                                                    {((reportData.status.includes('Active') || reportData.status === 'Rejected' || reportData.status.includes('Closed & Pending'))) || (reportData.status.includes('Closed & Pending')) ? (
                                                        <div className="">
                                                            <h4 className="text-header ">DCSO Comments</h4>
                                                            <p className="info">{dcop?.dcsoComments || 'No comments'}</p>
                                                        </div>
                                                    ) : null}
                                                </div>
                                            </div>
                                        ) : null}
                                    </div>
                                }
                            </div>


                            <div className="section p-1 row">
                                {reportData.permitType === 'CA' && (<>
                                    <div className="col-6 ">
                                        <h4 className="text-header mt-3">Applicant</h4>
                                        <p style={{ textAlign: 'justify' }}>
                                            I confirm that I am the supervisor in charge of the activity(ies)
                                            and am suitably competent to carry out the Person-In-Charge
                                            function. I have read and fully understand the SWMS/SWP and
                                            all the safety precautions to be taken under current legislations
                                            and STT GDC's Group Minimum Standards. I confirm all
                                            SWMS/SWP & Safety Checklist Conditions are complied with.
                                            To the best of my knowledge, this activity is safe to proceed.</p>
                                        <div>
                                            {reportData.high_risk?.applicantSign && (
                                                <img src={applicantSign1} alt="Applicant Signature" style={{ width: '200px' }} />
                                            )}
                                            <p className="info">{reportData.high_risk.applicantName}</p>
                                            <p className="info">Signed on: {reportData.high_risk.applicantSignedDate}</p>
                                        </div>
                                    </div>




                                    <div className="col-6">

                                        {reportData.high_risk && reportData.high_risk.assessorSign && <>


                                            {reportData.status.includes('Active') || reportData.status.includes('Approval') || reportData.status === 'Rejected' || reportData.status === 'Revoked' || reportData.status.includes("Closed") ? (
                                                <div className=" ">
                                                    <h4 className="text-header  mt-3">Assessor</h4>
                                                    <p style={{ textAlign: 'justify' }}>I confirm that I hold the appropriate qualification and
                                                        competence to assess this permit. I have reviewed all
                                                        aspects of the task(s)/activity(ies) and confirm that all
                                                        SWMS/SWP & Safety Checklist conditions are complied
                                                        with. To the best of my knowledge, this activity is safe to
                                                        proceed.</p>
                                                    <div>
                                                        {reportData.high_risk?.assessorSign && (
                                                            <img src={assessorSign} alt="Assessor Signature" style={{ width: '200px' }} />
                                                        )}
                                                        <p className="info">{reportData.high_risk?.assessorName}</p>
                                                        <p className="info">Signed on: {reportData.high_risk?.assessorSignedDate}</p>
                                                        {((reportData.status.includes('Active') || reportData.status.includes('Approval') || reportData.status === 'Rejected')) || (reportData.status.includes('Approval')) ? (
                                                            <div className="">
                                                                <h4 className="text-header mt-3">Assessor Comments</h4>
                                                                <p className="info">{reportData.high_risk?.assessorComments || 'No comments'}</p>
                                                            </div>
                                                        ) : null}
                                                    </div>
                                                </div>
                                            ) : null}

                                        </>}
                                    </div>
                                    <div className="col-6">


                                        {reportData.status.includes('Active') || reportData.status.includes('DCSO') || reportData.status === 'Rejected' || reportData.status === 'Revoked' || reportData.status.includes("Closed") || reportData.status.includes('Closed & Pending') || reportData.status.includes('Pending DCSO Isolation / Acknowledgement') ? (
                                            <div className=" ">
                                                <h4 className="text-header  mt-3 ">Approver</h4>
                                                <p style={{ textAlign: 'justify' }}>I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).</p>
                                                <div>
                                                    {reportData.high_risk?.approverSign && (
                                                        <img src={approverSign} alt="Approver Signature" style={{ width: '200px' }} />
                                                    )}
                                                    <p className="info">{reportData.high_risk?.approverName}</p>
                                                    <p className="info">Signed on: {reportData.high_risk?.approverSignedDate}</p>
                                                    {((reportData.status.includes('Active') || reportData.status === 'Rejected' || reportData.status.includes('Closed & Pending'))) || ((reportData.status.includes('DCSO') || reportData.status.includes('Closed & Pending'))) || reportData.status.includes('Pending DCSO Isolation / Acknowledgement') ? (
                                                        <div className=" ">
                                                            <h4 className="text-header mt-3">Approver Comments</h4>
                                                            <p className="info">{reportData.high_risk?.approverComments || 'No comments'}</p>
                                                        </div>
                                                    ) : null}
                                                </div>
                                            </div>
                                        ) : null}


                                    </div>


                                </>)}



                            </div>

                            <div className="section p-1">
                                <div className="row">
                                    <div className="col-6">

                                        {reportData.closure && <>


                                            <div className=" ">
                                                <h4 className="text-header mt-3">Revoked</h4>
                                                <p>{reportData.closure?.suspendComments} </p>
                                                <div>

                                                    <p className="info">{reportData.closure?.suspendedBy}</p>
                                                    <p className="info">Signed on: {reportData.closure?.suspendedDate

                                                    }</p>
                                                </div>
                                            </div>

                                        </>}

                                        {reportData.closure && reportData.closure.applicantSign && <>

                                            <div className=" ">
                                                <h4 className="text-header mt-3">Closure</h4>
                                                <p>I declare that I have closed the permit and kept the workplace clean..... </p>
                                                <div>
                                                    {reportData.closure?.applicantSign && (
                                                        <img src={applicantCloseSign} alt="Assessor Signature" style={{ width: '200px' }} />
                                                    )}
                                                    <p className="info">{reportData.applicant.firstName}</p>
                                                    <p className="info">Signed on: {reportData.closure?.applicantSignedDate
                                                    }</p>
                                                </div>
                                            </div>


                                        </>}
                                    </div>







                                    {reportData?.closure?.dcso && reportData?.closure?.dcso.map((item, i) => {
                                        return (
                                            <div className="col-6 ">
                                                <h4 className="text-header mt-3">Normalization Action {i + 1} </h4>
                                                {item.status === "Returned" ?
                                                    <div>
                                                        <p className="info">{item.name}</p>
                                                        <p className="info">Comments: {item.dcsoNormComments
                                                        }</p>
                                                        <p>Returned on: {item.dcsoReturnedDate}</p>
                                                    </div>

                                                    :
                                                    <div>
                                                        <p style={{ textAlign: 'justify' }}>I confirm that where applicable, isolation has been normalized
                                                            in compliance with current legislations and STT GDC's
                                                            requirements. To the best of my knowledge, the work area and
                                                            the equipment have been placed in a safe condition to
                                                            continue operation.</p>
                                                        {item?.dcsoNormSign && (
                                                            <img src={dcsoNormSignedDate} alt="Assessor Signature" style={{ width: '200px' }} />
                                                        )}
                                                        <p className="info">{item.name}</p>
                                                        {item.dcsoNormSignedDate &&
                                                            <p className="info">Signed on: {item.dcsoNormSignedDate

                                                            }</p>}
                                                        {item.dcsoNormComments &&
                                                            <p className="info">Comments: {item.dcsoNormComments
                                                            }</p>}
                                                    </div>
                                                }
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>


                        </div>
                    )}

                    {(reportData.status === "Pending DCSO Isolation / Acknowledgement" && applcationDetail.assignedToId === user.id) && <>


                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row className="justify-content-center">

                                        <Col xs={8} sm={8} md={8} className="d-flex  text-center">
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Approve')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Approve' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Approve' ?
                                                                <span class="material-icons" style={apiStatus === 'Approve' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={6} sm={6} md={6} style={apiStatus === 'Approve' ? { color: 'green' } : {}}>
                                                        Acknowledge
                                                    </Col>
                                                </Row>


                                            </Container>
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Return')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Return' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Return' ?
                                                                <span class="material-icons" style={apiStatus === 'Return' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={8} sm={8} md={8} style={apiStatus === 'Return' ? { color: 'red' } : {}}>
                                                        Return
                                                    </Col>
                                                </Row>


                                            </Container>

                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>


                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            {/* Main container column with automatic margin and bottom margin for spacing */}
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                {/* Card with a subtle shadow effect to give it a raised look */}
                                <Card.Body>
                                    {/* Card body to contain the main content */}
                                    <Row className="justify-content-center">
                                        {/* Row to center the content horizontally */}
                                        <Col xs={8} sm={8} md={8} className="d-flex text-center">
                                            {/* Column with flexbox and centered text to align content in the center */}

                                            {/* Add your TextArea input here */}
                                            <textarea
                                                rows="4"
                                                cols="50"
                                                className="form-control"
                                                placeholder="Enter your comments here..."
                                                onChange={(e) => setDcsoComments(e.target.value)}
                                            />
                                            {/* A textarea element for user input, with 4 rows and 50 columns for size */}
                                        </Col>
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                        {apiStatus === 'Approve' &&
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                    <Card.Body >
                                        <Row>

                                            <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                                <label style={{ textAlign: 'justify' }}>I acknowledge this permit application. I confirm that where applicable, isolation has been completed in compliance with current legislations and STT GDC's requirements.</label>
                                            </Col>

                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true) }}>
                                                <span class="material-icons" style={{ fontSize: 60 }}>
                                                    draw
                                                </span>


                                            </Col>

                                            <div className="d-flex justify-content-center">
                                                {signs !== '' &&
                                                    <img src={signs} height={100} style={{ minWidth: 150 }} />
                                                }
                                            </div>

                                        </Row>



                                    </Card.Body>

                                </Card>
                            </Col>}

                    </>}

                    {(reportData.status === "Active" || reportData.status === "Active: Timed Out") && (reportData.applicantId
                        === user.id) && <>

                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                    <Card.Body >

                                        <Form>
                                            <Form.Group controlId="custom-switch" className="d-flex align-items-center">
                                                <Form.Check
                                                    type="switch"
                                                    checked={isChecked}
                                                    onChange={handleSwitchChange}
                                                    style={{ marginRight: '10px', marginTop: '-20px' }} // Add space between the switch and label
                                                />
                                                <Form.Label className="m-0">
                                                    {reportData.closure?.status === 'Return' ?
                                                        'Reassign DCSO for Normalization'
                                                        : 'The task(s) have been completed. The work area(s) have been left in a tidy and safe condition'
                                                    }

                                                </Form.Label>
                                            </Form.Group>

                                            {(isChecked && !reportData.closure && reportData.closure?.status !== 'Return') && <>
                                                <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true) }}>
                                                    <span class="material-icons" style={{ fontSize: 60 }}>
                                                        draw
                                                    </span>
                                                </Col>
                                                <div className="d-flex justify-content-center">
                                                    {signs !== '' &&
                                                        <img src={signs} height={100} style={{ minWidth: 150 }} />
                                                    }
                                                </div>
                                            </>
                                            }
                                        </Form>
                                    </Card.Body>
                                </Card>
                            </Col>
                            {isChecked &&
                                <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                    <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                        <Card.Body>

                                            <Form.Group controlId="formSelect">
                                                <Form.Label>
                                                    {'Identify DCSO for normalization'} <span style={{ color: '#D62828' }}>*</span>
                                                </Form.Label>
                                                <Select
                                                    value={selectedDcso}
                                                    onChange={(e) => setSelectedDcso(e)}
                                                    options={dcsoApprover}
                                                    placeholder={'Select...'}
                                                    isSearchable


                                                />
                                            </Form.Group>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            }
                        </>}
                    {(reportData.status === "Pending DCSO Action" && applcationDetail.assignedToId === user.id) && <>


                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row className="justify-content-center">

                                        <Col xs={8} sm={8} md={8} className="d-flex  text-center">
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Approve')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Approve' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Approve' ?
                                                                <span class="material-icons" style={apiStatus === 'Approve' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={6} sm={6} md={6} style={apiStatus === 'Approve' ? { color: 'green' } : {}}>
                                                        Acknowledge
                                                    </Col>
                                                </Row>


                                            </Container>
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Return')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Return' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Return' ?
                                                                <span class="material-icons" style={apiStatus === 'Return' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={8} sm={8} md={8} style={apiStatus === 'Return' ? { color: 'red' } : {}}>
                                                        Return
                                                    </Col>
                                                </Row>


                                            </Container>

                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>


                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            {/* Main container column with automatic margin and bottom margin for spacing */}
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                {/* Card with a subtle shadow effect to give it a raised look */}
                                <Card.Body>
                                    {/* Card body to contain the main content */}
                                    <Row className="justify-content-center">
                                        {/* Row to center the content horizontally */}
                                        <Col xs={8} sm={8} md={8} className="d-flex text-center">
                                            {/* Column with flexbox and centered text to align content in the center */}

                                            {/* Add your TextArea input here */}
                                            <textarea
                                                rows="4"
                                                cols="50"
                                                className="form-control"
                                                placeholder="Enter your comments here..."
                                                onChange={(e) => setDcsoComments(e.target.value)}
                                            />
                                            {/* A textarea element for user input, with 4 rows and 50 columns for size */}
                                        </Col>
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                        {apiStatus === 'Approve' &&
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                    <Card.Body >
                                        <Row>

                                            <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                                <label style={{ textAlign: 'justify' }}>I acknowledge this permit application.  isolation has been normalized in compliance with current legislations and STT GDC's requirements.</label>
                                            </Col>

                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true) }}>
                                                <span class="material-icons" style={{ fontSize: 60 }}>
                                                    draw
                                                </span>


                                            </Col>

                                            <div className="d-flex justify-content-center">
                                                {signs !== '' &&
                                                    <img src={signs} height={100} style={{ minWidth: 150 }} />
                                                }
                                            </div>

                                        </Row>



                                    </Card.Body>

                                </Card>
                            </Col>}

                    </>}

                    {(reportData.status === 'Active' && reportData.applicantId !== user.id) && <>

                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body >
                                    <Form>
                                        <Form.Group controlId="custom-switch" className="d-flex align-items-center">
                                            <Form.Check
                                                type="switch"
                                                checked={isChecked}
                                                onChange={handleSwitchChange}
                                                style={{ marginRight: '10px', marginTop: '-20px' }} // Add space between the switch and label
                                            />
                                            <Form.Label className="m-0">
                                                Is there a nedd to suspend the permit for any reason ?
                                            </Form.Label>
                                        </Form.Group>

                                        {isChecked &&
                                            <Row className="justify-content-center mt-3">
                                                <Col xs={8} sm={8} md={8} className="d-flex text-center">
                                                    <textarea
                                                        rows="4"
                                                        cols="50"
                                                        className="form-control"
                                                        placeholder="Enter your comments here..."
                                                        onChange={(e) => setDcsoComments(e.target.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        }
                                    </Form>
                                </Card.Body>
                            </Card>
                        </Col>

                    </>


                    }

                    {(reportData.status === "Pending HRA Assessment" || reportData.status === "Pending HRA Approval") && <>
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                <Card.Body >
                                    <Row className="justify-content-center">

                                        <Col xs={8} sm={8} md={8} className="d-flex  text-center">
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Approve')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Approve' ? { width: 24, height: 24, borderRadius: 12, background: 'green' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Approve' ?
                                                                <span class="material-icons" style={apiStatus === 'Approve' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={6} sm={6} md={6} style={apiStatus === 'Approve' ? { color: 'green' } : {}}>
                                                        Approve
                                                    </Col>
                                                </Row>


                                            </Container>
                                            <Container fluid className="col-5 p-2" style={{ border: '1px solid #dee2e6', borderRadius: 10, color: '#000000' }} onClick={() => setApiStatus('Return')}>
                                                <Row   >
                                                    <Col xs={4} sm={4} md={4}>
                                                        <div style={apiStatus === 'Return' ? { width: 24, height: 24, borderRadius: 12, background: 'red' } : { width: 24, height: 24, borderRadius: 12, background: 'lightgray' }} >
                                                            {apiStatus === 'Return' ?
                                                                <span class="material-icons" style={apiStatus === 'Return' ? { color: 'white' } : {}}>
                                                                    done
                                                                </span>
                                                                : ''}
                                                        </div>
                                                    </Col>
                                                    <Col xs={8} sm={8} md={8} style={apiStatus === 'Return' ? { color: 'red' } : {}}>
                                                        Reject
                                                    </Col>
                                                </Row>


                                            </Container>

                                        </Col>
                                    </Row>



                                </Card.Body>

                            </Card>
                        </Col>


                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            {/* Main container column with automatic margin and bottom margin for spacing */}
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                {/* Card with a subtle shadow effect to give it a raised look */}
                                <Card.Body>
                                    {/* Card body to contain the main content */}
                                    <Row className="justify-content-center">
                                        {/* Row to center the content horizontally */}
                                        <Col xs={8} sm={8} md={8} className="d-flex text-center">
                                            {/* Column with flexbox and centered text to align content in the center */}

                                            {/* Add your TextArea input here */}
                                            <textarea
                                                rows="4"
                                                cols="50"
                                                className="form-control"
                                                placeholder="Enter your comments here..."
                                                onChange={(e) => setDcsoComments(e.target.value)}
                                            />
                                            {/* A textarea element for user input, with 4 rows and 50 columns for size */}
                                        </Col>
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                        {apiStatus === 'Approve' && <>
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                    <Card.Body >
                                        <Row>

                                            <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                                <label style={{ textAlign: 'justify' }}>I confirm that I hold the appropriate qualification and competence to assess this permit. I have reviewed all aspects of the task(s)/activity(ies) and have confirmed all SWMS/SWP & Safety Checklist conditions are complied with. To the best of my knowledge this activity is safe to proceed.</label>
                                            </Col>

                                            <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => { setSearchModal(true) }}>
                                                <span class="material-icons" style={{ fontSize: 60 }}>
                                                    draw
                                                </span>


                                            </Col>

                                            <div className="d-flex justify-content-center">
                                                {signs !== '' &&
                                                    <img src={signs} height={100} style={{ minWidth: 150 }} />
                                                }
                                            </div>

                                        </Row>



                                    </Card.Body>

                                </Card>
                            </Col>

                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>

                                    <Card.Body >
                                        <Row>
                                            <label className="form-label fw-bold">{reportData.status === "Pending HRA Assessment" ? 'High Risk Approver' : 'DCSO Representative'} </label>
                                            <Select
                                                value={highRiskApprover.find(option => option.value === selectedApprover)}
                                                onChange={(selectedOption) => setSelectedApprover(selectedOption)}
                                                options={highRiskApprover}
                                                placeholder="Choose "
                                                isClearable
                                                classNamePrefix="react-select"
                                            />

                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>

                        </>}
                    </>}








                </Modal.Body>

                <Modal.Footer className="flex-wrap">
                    {

                        <>
                            {(reportData.status === "Pending DCSO Isolation / Acknowledgement" && applcationDetail.assignedToId === user.id) &&
                                <Button variant="primary" onClick={() => validationCheck()}>Submit</Button>
                            }

                            {(reportData.status === "Pending HRA Assessment" || reportData.status === "Pending HRA Approval") &&

                                <Button variant="primary" onClick={() => validationAssessor()}>{apiStatus === 'Approve' ? 'Approve' : 'Reject'}</Button>

                            }
                            {/* {(reportData.status === "Active" || reportData.status === "Active: Timed Out") && (reportData.applicantId
                                === user.id) && <>
                                    {!isChecked ?
                                        <Button variant="primary" onClick={() => validationCheck()}>ClonePermit</Button>
                                        : <Button variant="primary" onClick={() => ClosePermit()}>Closeout</Button>
                                    }


                                </>
                            } */}
                            {(reportData.status === "Pending DCSO Action" && applcationDetail.assignedToId === user.id) &&

                                <Button variant="primary" onClick={() => validationCheckNormalization()}>{apiStatus === 'Approve' ? 'Act And Closeout' : 'Return to Applicant'}</Button>

                            }

                            {/* {(reportData.status !== 'Active' || reportData.status !== "Active: Timed Out" || reportData.status !== "Pending DCSO Action" || reportData.status !== "Pending DCSO Action" || reportData.status !== "Normalized and Closed") && (reportData.applicantId === user.id) &&
                                <Button variant="primary" onClick={() => withDraw()}>Withdraw</Button>
                            } */}

                            {/* {(reportData.status === 'Active' && reportData.applicantId !== user.id) &&
                                isChecked && <Button variant="primary" onClick={() => suspendPermit()}>Revoked</Button>
                            } */}

                            <Button variant="light" onClick={() => setShowReportModal(false)}>Close</Button>

                        </>

                    }

                </Modal.Footer>
            </Modal>

            <Modal
                show={searchModal}
                onHide={() => { setSearchModal(false) }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                backdrop={'static'}
            >
                <Modal.Header closeButton={false}>
                    <Modal.Title id="contained-modal-title-vcenter">
                        Sign
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>
                    <SignatureCanvas
                        ref={sign}
                        penColor='green'
                        backgroundColor="white"
                        canvasProps={{
                            className: "sigCanvas",
                            style: {
                                width: '100%', // Ensures the canvas takes up the full width
                                background: '#fff',
                                boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                                height: '100px'
                            },
                        }}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => { setSign(sign.current.getTrimmedCanvas().toDataURL("image/png")); setSearchModal(false) }}>confirm</Button>
                    <Button onClick={() => sign.current.clear()}>Clear</Button>
                    <Button onClick={() => { setSearchModal(false) }}>Close</Button>
                </Modal.Footer>
            </Modal>

            <Modal
                show={isoModal}
                onHide={() => { setIsoModal(false) }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                backdrop={'static'}
            >
                <Modal.Header closeButton={false}>
                    <Modal.Title id="contained-modal-title-vcenter">
                        Fire System Isolation
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>
                    {fs.map((item, index) => {
                        console.log(item)
                        return (
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                    {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteFS(index) }} >close</i>}
                                    <Card.Body >
                                        <Row  >
                                            <Row>
                                                <Col xs={12} sm={12} md={12} className="d-flex ">
                                                    <label>Fire system(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                </Col>
                                            </Row>
                                            <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                <Container className="d-flex flex-wrap">
                                                    {item.systems && item.systems.map((tag) => {
                                                        return (tagComponent(tag))
                                                    })

                                                    }



                                                </Container>

                                            </Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                console.log(data)
                                                var locations = fs
                                                locations[index].loc5 = data
                                                setFS(locations)
                                                fetchLocationSix(data.id)

                                                forceUpdate()
                                            }} type={'five'} searchList={searchValue} box={false} />
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                            } updateListSelected={data => {
                                                var locations = fs
                                                locations[index].loc6 = data
                                                setFS(locations)

                                                forceUpdate()

                                            }} type={'six'} searchList={searchValue} box={false} />
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        )

                    })}
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => setFireSystemsIsolation()}>Isolate</Button>
                    <Button onClick={() => { setIsoModal(false) }}>Close</Button>
                </Modal.Footer>
            </Modal>

            <Modal
                show={isoSSModal}
                onHide={() => { setIsoSSModal(false) }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                backdrop={'static'}
            >
                <Modal.Header closeButton={false}>
                    <Modal.Title id="contained-modal-title-vcenter">
                        Security System Isolation
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>

                    {ss.map((item, index) => {
                        return (
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                    {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteSS(index) }} >close</i>}
                                    <Card.Body >
                                        <Row  >
                                            <Row>
                                                <Col xs={12} sm={12} md={12} className="d-flex ">
                                                    <label>Security System(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                </Col>
                                            </Row>
                                            <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                <Container className="d-flex flex-wrap">
                                                    {console.log(item.systems)}
                                                    {item.systems && item.systems.map((tag) => {
                                                        return (tagComponent(tag))
                                                    })

                                                    }

                                                </Container>

                                            </Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                console.log(data)
                                                var locations = ss
                                                locations[index].loc5 = data
                                                setSS(locations)
                                                fetchLocationSix(data.id)
                                                forceUpdate()
                                            }} type={'five'} searchList={searchValue} box={false} />
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                            } updateListSelected={data => {
                                                var locations = ss
                                                locations[index].loc6 = data
                                                setSS(locations)

                                                forceUpdate()

                                            }} type={'six'} searchList={searchValue} box={false} />

                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        )

                    })}
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => setSecuritySystemsIsolation()}>Isolate</Button>
                    <Button onClick={() => { setIsoSSModal(false) }}>Close</Button>
                </Modal.Footer>
            </Modal>

            <Modal
                show={isoEnergyModal}
                onHide={() => { setIsoSSModal(false) }}
                aria-labelledby="contained-modal-title-vcenter"
                centered
                backdrop={'static'}
            >
                <Modal.Header closeButton={false}>
                    <Modal.Title id="contained-modal-title-vcenter">
                        Energy System Isolation
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>

                    {es.map((item, index) => {
                        return (
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px', position: 'relative' }}>
                                    {index !== 0 && <i className="material-icons" style={{ color: 'red', position: 'absolute', right: 10, top: 5 }} onClick={() => { deleteSS(index) }} >close</i>}
                                    <Card.Body >
                                        <Row  >
                                            <Row>
                                                <Col xs={12} sm={12} md={12} className="d-flex ">
                                                    <label>Energy System(s) to be isolated  <span style={{ color: '#D62828' }}>*</span></label>
                                                </Col>
                                            </Row>
                                            <Row className="col-12 mt-2 mb-2 m-auto" style={{ borderRadius: '20px' }} >

                                                <Container className="d-flex flex-wrap">
                                                    {console.log(item.systems)}
                                                    {item.systems && item.systems.map((tag) => {
                                                        return (tagComponent(tag))
                                                    })

                                                    }

                                                </Container>

                                            </Row>
                                            <Col xs={12} sm={12} md={12} className="d-flex text-start mb-2 mt-2 ">
                                                <label>Location of the system(s) <span style={{ color: '#D62828' }}>*</span></label>
                                            </Col>
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[4].altTitle : ''} data={locationFive} selectedValue={item.loc5 ? item.loc5.id : ''} label={item.loc5.name || 'Select'} updateListSelected={data => {
                                                console.log(data)
                                                var locations = es
                                                locations[index].loc5 = data
                                                setES(locations)
                                                fetchLocationSix(data.id)
                                                forceUpdate()
                                            }} type={'five'} searchList={searchValue} box={false} />
                                            <SelectCardComponent mandatory={true} title={title.length !== 0 ? title[5].altTitle : ''} data={locationSix} selectedValue={item.loc6 ? item.loc6.id : ''} label={item.loc6.name || 'Select'
                                            } updateListSelected={data => {
                                                var locations = es
                                                locations[index].loc6 = data
                                                setES(locations)

                                                forceUpdate()

                                            }} type={'six'} searchList={searchValue} box={false} />

                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        )

                    })}
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={() => setEnergySystemsIsolation()}>Isolate</Button>
                    <Button onClick={() => { setIsoEnergyModal(false) }}>Close</Button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default PermitModal;