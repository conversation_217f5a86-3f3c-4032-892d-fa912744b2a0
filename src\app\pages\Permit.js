import React, { useState, useEffect } from "react";
import MaterialTable from 'material-table';
import { ThemeProvider, createTheme } from '@mui/material';
import { eptwColumns, tableOptions } from './TableColumns';
import PermitModal from './PermitModal';
import { PERMIT_REPORTS, PERMIT_REPORT_WITH_ID, STATIC_URL } from "../constants";
import API from "../services/API";
import AllFilterLocation from "./AllLocationFilter";

export const Permit = ({ status, data }) => {

    const [permits, setPermits] = useState([]);
    const [filterData, setFilterData] = useState([]);

    useEffect(() => {
        getPermits();
    }, [])

    const getPermits = async () => {
        const response = await API.get(PERMIT_REPORTS);
        if (response.status === 200) {
            setPermits(response.data.filter(i => i.status === status))
            setFilterData(response.data.filter(i => i.status === status))
        }
    }

    const handleFilter = (locationOneId, locationTwoId, locationThreeId, locationFourId) => {
        const filteredData = permits.filter(item => {
            return (
                (locationOneId === '' || item.locationOneId === locationOneId) &&
                (locationTwoId === '' || item.locationTwoId === locationTwoId) &&
                (locationThreeId === '' || item.locationThreeId === locationThreeId) &&
                (locationFourId === '' || item.locationFourId === locationFourId)
            );
        });

        setFilterData(filteredData);
    };


    const [showReportModal, setShowReportModal] = useState(false);
    const [reportData, setReportData] = useState(null);
    const viewObservationReport = async (id) => {

        const params = {
            "include": [{ "relation": "permitReportAction" }, { "relation": "applicant" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "locationFive" }, { "relation": "locationSix" }]

        };
        const response = await API.get(`${PERMIT_REPORT_WITH_ID(id)}?filter=${encodeURIComponent(JSON.stringify(params))}`);

        if (response.status === 200) {

            const actionUploads = (response.data.permitReportActions && response.data.permitReportActions.length) ? response.data.permitReportActions.flatMap(obj => obj.uploads) : [];

            response.data.uploads = [...response.data.uploads]

            response.data.uploads = response.data.uploads ? response.data.uploads.map(i => {
                return { src: `${STATIC_URL}/${i}`, width: 4, height: 3 }
            }) : []


            setReportData(response.data)
            setShowReportModal(true)
        }

    }
    const defaultMaterialTheme = createTheme();

    const tableStyle = {
        borderRadius: '0',
        boxShadow: 'none',
    };

    const tableActions = [
        {
            icon: 'visibility',
            tooltip: 'View Report',
            onClick: (event, rowData) => {
                // Do save operation
                console.log(rowData)
                viewObservationReport(rowData.id)
            }
        }
    ]

    const localization = {
        header: {
            actions: 'View'
        }
    };



    return (
        <>
            <AllFilterLocation handleFilter={handleFilter} disableAll={false} period={true} />
            <ThemeProvider theme={defaultMaterialTheme}>
                <MaterialTable
                    columns={eptwColumns}
                    data={permits}
                    title=""
                    style={tableStyle}
                    actions={tableActions}
                    options={{ pageSize: 20 }}
                    localization={localization}
                />
            </ThemeProvider>

            <PermitModal reportData={reportData} showReportModal={showReportModal} setShowReportModal={(status) => setShowReportModal(status)} />
        </>
    )
}

export default Permit;